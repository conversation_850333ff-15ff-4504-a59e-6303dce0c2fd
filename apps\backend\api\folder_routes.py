import logging

logger = logging.getLogger(__name__)

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, Depends, Query
from typing import Optional, List
from schemas.folder_schema import FolderCreate, FolderRename, FolderMove, FolderId, MergeFoldersInput, FolderGet

from services.auth_service import auth_guard
from services.folder_service import FolderService
from core.constants import Roles
from database.db import get_db

from utils.jwt_utils import JWTUtils
from exceptions.business_exceptions import AuthorizationError

router = APIRouter()



@router.post("/create_folder", response_model=FolderId)
async def create_folder(folder_in: FolderCreate,
                        db: AsyncSession = Depends(get_db),
                        user = Depends(auth_guard)):
    
    logger.info("[create_folder] Starting folder creation process")
    
    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    
    logger.info("[create_folder] User authentication successful - User ID: %s, Roles: %s", user_id, user_roles)

    if Roles.create_folder not in user_roles:
        logger.warning("[create_folder] Access denied - User %s does not have create folder role", user_id)
        raise AuthorizationError(detail="User does not have create folder permission.")
    
    logger.info("[create_folder] User has create folder permission, proceeding with folder creation")
    logger.info("[create_folder] Folder creation input data: folder_name=%s, parent_folder_id=%s, user_reports_id_list=%s", 
                folder_in.folder_name, folder_in.parent_folder_id, folder_in.user_reports_id_list)
    
    folder_service = FolderService(db=db, user_id=user_id)
    logger.info("[create_folder] FolderService initialized successfully")

    result = await folder_service.create_folder(folder_in=folder_in)
    logger.info("[create_folder] Folder created successfully with ID: %s", result.folder_id)
    
    return result


@router.patch("/rename_folder")
async def rename_folder(folder_changes: FolderRename, 
                        db: AsyncSession = Depends(get_db),
                        user=Depends(auth_guard)):
    
    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    if Roles.rename_folder not in user_roles:
        logger.warning("[rename_folder] Access denied - User %s does not have rename folder role", user_id)
        raise AuthorizationError(detail="User does not have rename folder permission.")


    folder_service = FolderService(db=db, user_id=user_id)

    await folder_service.rename_folder(folder_changes)


@router.put("/move_file_between_folders")
async def move_file_between_folders(folder_move: FolderMove,
                                    db: AsyncSession = Depends(get_db),
                                    user=Depends(auth_guard)):
    

    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    if Roles.move_folder not in user_roles:
        logger.warning("[move_file_between_folders] Access denied - User %s does not have move folder role", user_id)
        raise AuthorizationError(detail="User does not have move folder permission.")

    folder_service = FolderService(db=db, user_id=user_id)
    await folder_service.move_file_between_folders(folder_move=folder_move)

@router.get("/get_folders")
async def get_folders(
                        hmac_folder_name: Optional[List[str]] = Query(None, description="Optional HMAC filter"),
                        db: AsyncSession = Depends(get_db),
                        user=Depends(auth_guard)):
    
    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    if Roles.view_folder_list not in user_roles:
        logger.warning("[get_folders] Access denied - User %s does not have view folder list role", user_id)
        raise AuthorizationError(detail="User does not have view folder list permission.")
    
    folder_ids_hmac = None
    folder_service = FolderService(db=db, user_id=user_id)
    if hmac_folder_name:
        folder_ids_hmac = await folder_service.filter_using_hmac_folder(hmac_filter=hmac_folder_name)
        logger.info(f"[get_folders] Folder IDs using hmac filter: {hmac_folder_name} => {folder_ids_hmac}")

    folders = await folder_service.get_folders_handler(folder_ids_hmac=folder_ids_hmac)


    return folders



@router.delete("/delete_folder/{folder_id}")
async def delete_folder(folder_id: str, 
                        db: AsyncSession = Depends(get_db),
                        user=Depends(auth_guard)):
    
    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    if Roles.delete_folder not in user_roles:
        logger.warning("[delete_folder] Access denied - User %s does not have delete folder role", user_id)
        raise AuthorizationError(detail="User does not have delete folder permission.")

    folder_service = FolderService(db=db, user_id=user_id)

    await folder_service.delete_folder(folder_id)


@router.put("/merge_folders")
async def merge_folders(merge_folders_id: MergeFoldersInput, 
                        db: AsyncSession = Depends(get_db),
                        user=Depends(auth_guard)):
    
    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    if Roles.merge_folder not in user_roles:
        logger.warning("[merge_folders] Access denied - User %s does not have merge folder role", user_id)
        raise AuthorizationError(detail="User does not have merge folder permission.")

    folder_service = FolderService(db=db, user_id=user_id)
    return await folder_service.merge_folders(folder_id= merge_folders_id.folder_id, folder_id_to_merge=merge_folders_id.folder_id_to_merge)


@router.get("/get_folders_except_contains_folderid")
async def get_folders(  folder_id: Optional[str] = Query(None, description="Optional folder ID"),
                        hmac_folder_name: Optional[List[str]] = Query(None, description="Optional HMAC filter"),
                        db: AsyncSession = Depends(get_db),
                        user=Depends(auth_guard)):
    
    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    if Roles.view_folder_list not in user_roles:
        logger.warning("[get_folders] Access denied - User %s does not have view folder list role", user_id)
        raise AuthorizationError(detail="User does not have view folder list permission.")
    
    folder_ids_hmac = None
    folder_service = FolderService(db=db, user_id=user_id)
    if hmac_folder_name:
        folder_ids_hmac = await folder_service.filter_using_hmac_folder(hmac_filter=hmac_folder_name)
        logger.info(f"[get_folders] Folder IDs using hmac filter: {hmac_folder_name} => {folder_ids_hmac}")

    folders = await folder_service.get_folders_except(folder_id=folder_id, folder_ids_hmac=folder_ids_hmac)


    return folders



@router.get("/get_folder_except_actual")
async def get_folders(  folder_id: Optional[str] = Query(None, description="Optional folder ID"),
                        hmac_folder_name: Optional[List[str]] = Query(None, description="Optional HMAC filter"),
                        db: AsyncSession = Depends(get_db),
                        user=Depends(auth_guard)):
    
    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    if Roles.view_folder_list not in user_roles:
        logger.warning("[get_folders] Access denied - User %s does not have view folder list role", user_id)
        raise AuthorizationError(detail="User does not have view folder list permission.")
    
    folder_ids_hmac = None
    folder_service = FolderService(db=db, user_id=user_id)
    if hmac_folder_name:
        folder_ids_hmac = await folder_service.filter_using_hmac_folder(hmac_filter=hmac_folder_name)
        logger.info(f"[get_folders] Folder IDs using hmac filter: {hmac_folder_name} => {folder_ids_hmac}")

    folders = await folder_service.get_folders_except_actual(folder_id=folder_id, folder_ids_hmac=folder_ids_hmac)

    return folders