import logging
import async<PERSON>
import httpx
from jose import jwt, JWTError
from fastapi import Response

from core.config import settings
from exceptions.business_exceptions import (
    FailFetchJwkError,
    TokenVerificationFailedError,
    SessionExpiredError,
    FailRefreshTokenError,
    MissingRefreshTokenError,
    LogoutFailKeycloakError
)

logger = logging.getLogger(__name__)

_jwk_cache = {}

async def get_jwk_key(kid: str):
    global _jwk_cache
    logger.info("[get_jwk_key] Looking for key ID: %s", kid)

    if not _jwk_cache:
        logger.info("[get_jwk_key] JWK cache is empty. Fetching from Keycloak...")
        try:
            max_attempts = 3
            for attempt in range(1, max_attempts + 1):
                try:
                    async with httpx.AsyncClient(verify=settings.KEYCLOAK_VERIFY_SSL, timeout=10.0) as client:
                        response = await client.get(settings.JWKS_URL)
                    logger.info("[get_jwk_key] Response status: %s (attempt %s/%s)", response.status_code, attempt, max_attempts)
                    response.raise_for_status()

                    _jwk_cache = {jwk["kid"]: jwk for jwk in response.json()["keys"]}
                    logger.info("[get_jwk_key] Keys cached: %s", list(_jwk_cache.keys()))
                    break
                except httpx.RequestError as e:
                    if attempt == max_attempts:
                        raise
                    backoff = 0.5 * (2 ** (attempt - 1))
                    logger.warning("[get_jwk_key] Network error fetching JWKs (attempt %s/%s): %s. Retrying in %.1fs", attempt, max_attempts, e, backoff)
                    await asyncio.sleep(backoff)
                except httpx.HTTPStatusError as e:
                    status = e.response.status_code if e.response else None
                    if status and 500 <= status < 600 and attempt < max_attempts:
                        backoff = 0.5 * (2 ** (attempt - 1))
                        logger.warning("[get_jwk_key] Server error %s fetching JWKs (attempt %s/%s). Retrying in %.1fs", status, attempt, max_attempts, backoff)
                        await asyncio.sleep(backoff)
                        continue
                    raise
        except Exception as e:
            logger.exception("[get_jwk_key] Failed to fetch JWKs: %s", e)
            raise FailFetchJwkError()

    key = _jwk_cache.get(kid)
    if not key:
        logger.warning("[get_jwk_key] Key %s not found in cache. Refreshing...", kid)
        _jwk_cache = {}
        return await get_jwk_key(kid)

    logger.info("[get_jwk_key] Key %s found.", kid)
    return key


async def verify_jwt(token: str):
    try:
        logger.info("[verify_jwt] Extracting token header...")
        unverified_header = jwt.get_unverified_header(token)
        logger.info("[verify_jwt] Token algorithm: %s, kid: %s", unverified_header["alg"], unverified_header["kid"])

        jwk = await get_jwk_key(unverified_header["kid"])

        logger.info("[verify_jwt] Decoding token...")
        claims = jwt.decode(
            token,
            jwk,
            algorithms=[unverified_header["alg"]],
            audience=settings.CLIENT_ID_KEYCLOAK,
            issuer="%s/realms/%s" % (settings.KEYCLOAK_URL, settings.REALM_NAME)
        )
        logger.info("[verify_jwt] Token decoded successfully.")
        return claims

    except JWTError as e:
        logger.error("[verify_jwt] Token verification failed: %s", e)
        raise TokenVerificationFailedError()


async def refresh_access_token(refresh_token: str):
    logger.info("[refresh_access_token] Attempting to refresh token...")
    logger.info("[refresh_access_token] Input parameters - refresh_token_length: %s, client_id: %s, refresh_url: %s", 
                len(refresh_token) if refresh_token else 0, 
                settings.CLIENT_ID_KEYCLOAK, 
                settings.REFRESH_URL)
    
    # Log Keycloak connectivity check before attempting refresh
    logger.info("[refresh_access_token] Checking Keycloak connectivity before refresh attempt...")
    try:
        async with httpx.AsyncClient(verify=settings.KEYCLOAK_VERIFY_SSL, timeout=5.0) as client:
            health_url = f"{settings.KEYCLOAK_URL}/health"
            logger.info("[refresh_access_token] Testing Keycloak health endpoint: %s", health_url)
            health_response = await client.get(health_url)
            logger.info("[refresh_access_token] Keycloak health check - status: %s, healthy: %s", 
                       health_response.status_code, health_response.status_code == 200)
    except Exception as health_error:
        logger.warning("[refresh_access_token] Keycloak health check failed: %s", str(health_error))
        logger.warning("[refresh_access_token] This may indicate network connectivity issues with Keycloak")
    
    data = {
        "grant_type": "refresh_token",
        "client_id": settings.CLIENT_ID_KEYCLOAK,
        "client_secret": settings.CLIENT_SECRET_KEYCLOAK,
        "refresh_token": refresh_token
    }

    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    
    # Log request details (without exposing sensitive data)
    logger.info("[refresh_access_token] Request details - grant_type: %s, client_id: %s, client_secret_length: %s, refresh_token_length: %s", 
                data["grant_type"], data["client_id"], len(data["client_secret"]) if data["client_secret"] else 0, len(data["refresh_token"]) if data["refresh_token"] else 0)
    logger.info("[refresh_access_token] Request headers: %s", headers)
    logger.info("[refresh_access_token] Keycloak configuration - URL: %s, realm: %s, verify_ssl: %s", 
               settings.KEYCLOAK_URL, settings.REALM_NAME, settings.KEYCLOAK_VERIFY_SSL)

    try:
        max_attempts = 3
        for attempt in range(1, max_attempts + 1):
            try:
                logger.info("[refresh_access_token] Creating HTTP client for refresh attempt %s/%s...", attempt, max_attempts)
                logger.info("[refresh_access_token] HTTP client settings - verify_ssl: %s, timeout: 10.0s", settings.KEYCLOAK_VERIFY_SSL)
                
                async with httpx.AsyncClient(verify=settings.KEYCLOAK_VERIFY_SSL, timeout=10.0) as client:
                    logger.info("[refresh_access_token] Sending POST request to refresh endpoint...")
                    logger.info("[refresh_access_token] Request URL: %s", settings.REFRESH_URL)
                    logger.info("[refresh_access_token] Request data keys: %s", list(data.keys()))
                    logger.info("[refresh_access_token] Request headers: %s", headers)
                    
                    response = await client.post(settings.REFRESH_URL, data=data, headers=headers)
                    
                    logger.info("[refresh_access_token] Response received - status: %s, headers: %s (attempt %s/%s)", 
                               response.status_code, dict(response.headers), attempt, max_attempts)
                    
                    # Log response headers for debugging
                    logger.info("[refresh_access_token] Response headers - content_type: %s, content_length: %s, server: %s", 
                               response.headers.get("content-type"), 
                               response.headers.get("content-length"), 
                               response.headers.get("server"))

                logger.info("[refresh_access_token] Response status: %s (attempt %s/%s)", response.status_code, attempt, max_attempts)

                if response.status_code == 200:
                    logger.info("[refresh_access_token] Received successful response (200). Parsing response body...")
                    
                    try:
                        response_data = response.json()
                        logger.info("[refresh_access_token] Response body parsed successfully. Response keys: %s", list(response_data.keys()))
                        
                        access_token = response_data.get("access_token")
                        refresh_token = response_data.get("refresh_token")
                        expires_in = response_data.get("expires_in")
                        token_type = response_data.get("token_type")
                        
                        access_len = len(access_token or "")
                        refresh_len = len(refresh_token or "")
                        
                        logger.info("[refresh_access_token] Token extraction results - access_token_length: %s, refresh_token_length: %s, expires_in: %s, token_type: %s",
                                  access_len, refresh_len, expires_in, token_type)
                        
                        if not access_token:
                            logger.error("[refresh_access_token] Missing access_token in successful response body.")
                            logger.error("[refresh_access_token] Response data keys: %s", list(response_data.keys()))
                            raise FailRefreshTokenError()
                        
                        if not refresh_token:
                            logger.error("[refresh_access_token] Missing refresh_token in successful response body.")
                            logger.error("[refresh_access_token] Response data keys: %s", list(response_data.keys()))
                            raise FailRefreshTokenError()
                        
                        logger.info("[refresh_access_token] Token refresh completed successfully. access_token_length: %s, refresh_token_length: %s",
                                  access_len, refresh_len)
                        
                        return access_token, refresh_token
                        
                    except Exception as parse_error:
                        logger.error("[refresh_access_token] Failed to parse response JSON: %s", str(parse_error))
                        logger.error("[refresh_access_token] Response status: %s, content_type: %s, content_length: %s", 
                                   response.status_code, response.headers.get("content-type"), response.headers.get("content-length"))
                        logger.error("[refresh_access_token] Response text preview: %s", response.text[:500] if response.text else "No text content")
                        raise FailRefreshTokenError()

                # Non-200
                logger.error("[refresh_access_token] Received non-200 response. Status: %s, headers: %s", response.status_code, dict(response.headers))
                
                error_text = None
                error_data = None
                
                try:
                    error_text = response.text
                    logger.error("[refresh_access_token] Error response text: %s", error_text)
                    
                    # Try to parse error response as JSON for more detailed error information
                    try:
                        error_data = response.json()
                        logger.error("[refresh_access_token] Error response JSON: %s", error_data)
                        
                        # Log specific error details if available
                        error_type = error_data.get("error")
                        error_description = error_data.get("error_description")
                        if error_type:
                            logger.error("[refresh_access_token] Keycloak error type: %s, description: %s", error_type, error_description)
                            
                    except Exception as json_error:
                        logger.warning("[refresh_access_token] Could not parse error response as JSON: %s", str(json_error))
                        
                except Exception as e:
                    logger.error("[refresh_access_token] Failed to read error response text: %s", str(e))
                
                if response.status_code == 400 and error_text and "invalid_grant" in error_text.lower():
                    logger.warning("[refresh_access_token] invalid_grant detected. Error details - status: %s, error_text: %s, refresh_token_length: %s, client_id: %s", 
                                  response.status_code, error_text, len(refresh_token) if refresh_token else 0, settings.CLIENT_ID_KEYCLOAK)
                    
                    # Log additional context for invalid_grant errors
                    if error_data:
                        error_type = error_data.get("error")
                        error_description = error_data.get("error_description")
                        logger.warning("[refresh_access_token] Invalid grant details - error_type: %s, error_description: %s", error_type, error_description)
                    
                    logger.warning("[refresh_access_token] Invalid grant typically indicates expired refresh token or revoked session.")
                    logger.warning("[refresh_access_token] Forcing logout due to invalid_grant...")
                    
                    try:
                        await logout_user_using_token(refresh_token=refresh_token)
                        logger.info("[refresh_access_token] Successfully logged out user due to invalid_grant")
                    except Exception as logout_error:
                        logger.error("[refresh_access_token] Failed to logout user during invalid_grant handling: %s", str(logout_error))
                    
                    raise SessionExpiredError()

                # Retry on server errors (5xx) and connectivity issues (404 for ngrok/tunnel problems)
                if (500 <= response.status_code < 600 or response.status_code == 404) and attempt < max_attempts:
                    backoff = 0.5 * (2 ** (attempt - 1))
                    if response.status_code == 404:
                        logger.warning("[refresh_access_token] Connectivity issue detected (404 - possibly ngrok tunnel down). Error details - status: %s, error_text: %s, attempt: %s/%s, refresh_token_length: %s, client_id: %s. Retrying in %.1fs", 
                                      response.status_code, error_text, attempt, max_attempts, len(refresh_token) if refresh_token else 0, settings.CLIENT_ID_KEYCLOAK, backoff)
                    else:
                        logger.warning("[refresh_access_token] Server error detected. Error details - status: %s, error_text: %s, attempt: %s/%s, refresh_token_length: %s, client_id: %s. Retrying in %.1fs", 
                                      response.status_code, error_text, attempt, max_attempts, len(refresh_token) if refresh_token else 0, settings.CLIENT_ID_KEYCLOAK, backoff)
                    await asyncio.sleep(backoff)
                    continue

                logger.error("[refresh_access_token] Refresh failed permanently. Error details - status: %s, error_text: %s, attempt: %s/%s, refresh_token_length: %s, client_id: %s, refresh_url: %s", 
                            response.status_code, error_text, attempt, max_attempts, len(refresh_token) if refresh_token else 0, settings.CLIENT_ID_KEYCLOAK, settings.REFRESH_URL)
                raise FailRefreshTokenError()

            except httpx.RequestError as e:
                # Detailed network error logging
                error_type = type(e).__name__
                logger.error("[refresh_access_token] Network error detected - type: %s, message: %s", error_type, str(e))
                
                # Log specific error details based on error type
                if hasattr(e, 'request'):
                    logger.error("[refresh_access_token] Request details - method: %s, url: %s", 
                               e.request.method if e.request else "unknown", 
                               e.request.url if e.request else "unknown")
                
                if hasattr(e, 'response'):
                    logger.error("[refresh_access_token] Response details - status: %s, headers: %s", 
                               e.response.status_code if e.response else "unknown",
                               dict(e.response.headers) if e.response else "unknown")
                
                # Log network connectivity information
                logger.error("[refresh_access_token] Network connectivity issue - Keycloak URL: %s, verify_ssl: %s, timeout: 10.0s", 
                           settings.KEYCLOAK_URL, settings.KEYCLOAK_VERIFY_SSL)
                
                if attempt == max_attempts:
                    logger.error("[refresh_access_token] Network error on final attempt. Error details - exception: %s, exception_type: %s, attempt: %s/%s, refresh_token_length: %s, client_id: %s, refresh_url: %s", 
                                str(e), error_type, attempt, max_attempts, len(refresh_token) if refresh_token else 0, settings.CLIENT_ID_KEYCLOAK, settings.REFRESH_URL)
                    logger.error("[refresh_access_token] All network retry attempts exhausted. This may indicate persistent connectivity issues with Keycloak.")
                    
                    # Perform detailed network connectivity check on final failure
                    logger.info("[refresh_access_token] Performing detailed network connectivity check due to persistent failures...")
                    try:
                        connectivity_results = await check_network_connectivity()
                        logger.error("[refresh_access_token] Network connectivity check results: %s", connectivity_results)
                    except Exception as connectivity_error:
                        logger.error("[refresh_access_token] Failed to perform network connectivity check: %s", str(connectivity_error))
                    
                    raise FailRefreshTokenError()
                
                backoff = 0.5 * (2 ** (attempt - 1))
                logger.warning("[refresh_access_token] Network error detected. Error details - exception: %s, exception_type: %s, attempt: %s/%s, refresh_token_length: %s, client_id: %s, refresh_url: %s. Retrying in %.1fs", 
                              str(e), error_type, attempt, max_attempts, len(refresh_token) if refresh_token else 0, settings.CLIENT_ID_KEYCLOAK, settings.REFRESH_URL, backoff)
                logger.warning("[refresh_access_token] This may indicate temporary network connectivity issues or Keycloak service unavailability.")
                await asyncio.sleep(backoff)

        logger.error("[refresh_access_token] All refresh attempts exhausted. Final error details - max_attempts: %s, refresh_token_length: %s, client_id: %s, refresh_url: %s", 
                    max_attempts, len(refresh_token) if refresh_token else 0, settings.CLIENT_ID_KEYCLOAK, settings.REFRESH_URL)
        raise FailRefreshTokenError()
    except SessionExpiredError:
        raise
    except Exception as e:
        logger.error("[refresh_access_token] Unexpected error occurred. Error details - exception: %s, exception_type: %s, refresh_token_length: %s, client_id: %s, refresh_url: %s", 
                    str(e), type(e).__name__, len(refresh_token) if refresh_token else 0, settings.CLIENT_ID_KEYCLOAK, settings.REFRESH_URL)
        raise FailRefreshTokenError()


async def check_network_connectivity():
    """Check network connectivity to Keycloak and related services."""
    logger.info("[check_network_connectivity] Starting network connectivity check...")
    
    connectivity_results = {}
    
    # Check DNS resolution
    try:
        import socket
        from urllib.parse import urlparse
        
        parsed_url = urlparse(settings.KEYCLOAK_URL)
        hostname = parsed_url.hostname
        
        logger.info("[check_network_connectivity] Checking DNS resolution for: %s", hostname)
        ip_address = socket.gethostbyname(hostname)
        logger.info("[check_network_connectivity] DNS resolution successful - %s resolves to %s", hostname, ip_address)
        connectivity_results["dns_resolution"] = {"status": "success", "ip": ip_address}
        
    except Exception as dns_error:
        logger.error("[check_network_connectivity] DNS resolution failed for %s: %s", hostname, str(dns_error))
        connectivity_results["dns_resolution"] = {"status": "failed", "error": str(dns_error)}
    
    # Check basic connectivity to Keycloak
    try:
        async with httpx.AsyncClient(verify=settings.KEYCLOAK_VERIFY_SSL, timeout=5.0) as client:
            logger.info("[check_network_connectivity] Testing basic connectivity to Keycloak...")
            response = await client.get(settings.KEYCLOAK_URL)
            logger.info("[check_network_connectivity] Basic connectivity test - status: %s", response.status_code)
            connectivity_results["basic_connectivity"] = {"status": "success", "status_code": response.status_code}
            
    except Exception as conn_error:
        logger.error("[check_network_connectivity] Basic connectivity test failed: %s", str(conn_error))
        connectivity_results["basic_connectivity"] = {"status": "failed", "error": str(conn_error)}
    
    # Check health endpoint
    try:
        async with httpx.AsyncClient(verify=settings.KEYCLOAK_VERIFY_SSL, timeout=5.0) as client:
            health_url = f"{settings.KEYCLOAK_URL}/health"
            logger.info("[check_network_connectivity] Testing Keycloak health endpoint...")
            response = await client.get(health_url)
            logger.info("[check_network_connectivity] Health endpoint test - status: %s", response.status_code)
            connectivity_results["health_endpoint"] = {"status": "success", "status_code": response.status_code}
            
    except Exception as health_error:
        logger.error("[check_network_connectivity] Health endpoint test failed: %s", str(health_error))
        connectivity_results["health_endpoint"] = {"status": "failed", "error": str(health_error)}
    
    logger.info("[check_network_connectivity] Network connectivity check completed: %s", connectivity_results)
    return connectivity_results


async def logout_user_using_token(refresh_token: str, response: Response = None):
    if not refresh_token:
        logger.error("[logout_user_using_token] Missing refresh token.")
        raise MissingRefreshTokenError()

    url = "%s/realms/%s/protocol/openid-connect/logout" % (settings.KEYCLOAK_URL, settings.REALM_NAME)

    try:
        async with httpx.AsyncClient(verify=settings.KEYCLOAK_VERIFY_SSL, timeout=10.0) as client:
            res = await client.post(url, data={
                "client_id": settings.CLIENT_ID_KEYCLOAK,
                "client_secret": settings.CLIENT_SECRET_KEYCLOAK,
                "refresh_token": refresh_token
            })

        if res.status_code != 204:
            logger.error("[logout_user_using_token] Logout failed with status: %s", res.status_code)
            raise LogoutFailKeycloakError()

        logger.info("[logout_user_using_token] Logged out successfully.")

        if response:
            response.delete_cookie("refresh_token")
            response.delete_cookie("access_token")

        return {"message": "Logged out successfully"}

    except Exception as e:
        logger.exception("[logout_user_using_token] Unexpected error during logout: %s", e)
        raise LogoutFailKeycloakError()

