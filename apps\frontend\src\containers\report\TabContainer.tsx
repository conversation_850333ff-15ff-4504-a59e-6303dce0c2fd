import { cn } from "~/lib/utils";
import { Tabs } from '@snap/design-system'
import * as T from "../report/details/tabs"
import { usePermissionCheck } from "~/components/router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";
import { useReportType } from "~/store/reportDetailStore";
import { REPORT_CONSTANTS } from "~/helpers/constants";

const TabContainer = () => {
  const { checkPermission } = usePermissionCheck();
  const canUpdateReport = checkPermission(Permission.UPDATE_REPORT);
  const reportType = useReportType();
  const isRelacoes = reportType === REPORT_CONSTANTS.types.relacoes;

  const renderTabList = () => {
    const registriesTab = {
      value: 'registries',
      key: 'registries',
      label: 'índice',
      children: <T.ReportRecordsList />
    };
    const trashTab = {
      value: 'trash',
      key: 'trash',
      label: 'LIXEIRA',
      children: <T.ReportTrash />
    };

    if (!canUpdateReport || isRelacoes) {
      return [
        registriesTab
      ];
    }

    return [
      registriesTab,
      trashTab
    ];
  }

  return (
    <Tabs
      items={renderTabList()}
      className={cn(
        "[&_[role=tab]]:cursor-pointer [&_[role=tab]]:!border-none [&_[role=tabpanel]]:border-none [&_[role=tabpanel]]:px-3",
        "[&_[role=tab][data-state=inactive]_p]:opacity-20",
        "[&_[role=tab][data-state=inactive]>div>div]:bg-",
        "[&_[role=tab][data-state=inactive]>div>div]:bg-muted-foreground",
      )}
    />
  );
};

export default TabContainer;
