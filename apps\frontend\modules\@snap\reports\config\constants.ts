import reportTypes from "./word-map/report_types.json";
import reportLabels from "./word-map/labels.json";
import reportSections from "./word-map/ui_group_titles.json";
import translatedLabels from "./word-map/translate_prop_to_label.json"
import parsedValues from "./word-map/parsed_values.json";
import genericConstants from "./word-map/nomes_genericos.json";
import sourceLabels from "./word-map/source_labels.json";
import pluralWords from "./word-map/nomes_genericos.json";
import relacoesVinculos from "./word-map/relacoes_vinculos.json";

export const REPORT_CONSTANTS = reportTypes;
export const REPORT_LABELS = reportLabels;
export const REPORT_SECTIONS = reportSections;
export const GENERIC_CONSTANTS = genericConstants;
export const SOURCE_LABELS = sourceLabels;
export const TRANSLATED_LABELS = translatedLabels;
export const PARSED_VALUES = parsedValues;
export const PLURAL_WORDS = pluralWords.getPluralWord;
export const RELACOES_VINCULOS = relacoesVinculos;
