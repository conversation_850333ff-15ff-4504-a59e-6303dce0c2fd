import { Button } from '@snap/design-system'
import { useMemo, useState } from 'react'
import SemanticAnalisysTab from './SemanticAnalisysTab';
import { useVisibleReportSections } from '../../context/ReportContext';
import { REPORT_SECTIONS } from '../../config/constants';

const RenderRelacoes = () => {
  const [activeTab, setActiveTab] = useState('report')
  const sections = useVisibleReportSections();

  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
  };

  const relationshipData = useMemo(() => {
    const relationshipSection = sections.find(section =>
      section.title === REPORT_SECTIONS.relacoes
    );

    if (relationshipSection && relationshipSection.data) {
      return relationshipSection.data;
    }

    return [];
  }, [sections]);

  return (
    <div className='flex flex-col gap-4'>
      <div className="flex items-center">
        <Button
          className={`uppercase w-full !rounded-none ${activeTab === "report" && "!bg-foreground !text-background"}`}
          onClick={(e) => handleTabClick("report")}
          data-testid="button-create-report"
        >
          análise semântica
        </Button>
        <Button
          className={`uppercase w-full !rounded-none ${activeTab === "folder" && "!bg-foreground !text-background"}`}
          onClick={(e) => handleTabClick("folder")}
          data-testid="button-create-folder"
        >
          fluxo de relacionamento
        </Button>
      </div>

      <div className="flex-1 md:flex-3/4 max-h-[calc(100vh-200px)] overflow-y-auto overflow-x-hidden [scrollbar-gutter:stable] bg-background" id="report-content">
        {activeTab === "report" && (
          <div>
            <SemanticAnalisysTab data={relationshipData} />
          </div>
        )}
        {activeTab === "folder" && (
          <div>
            <h1>Fluxo de Relacionamento</h1>
          </div>
        )}
      </div>
    </div>
  )
}

export default RenderRelacoes