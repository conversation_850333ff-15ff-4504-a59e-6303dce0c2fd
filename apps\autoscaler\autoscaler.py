import os
import time
import requests
import docker
import logging

# Logging config
logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s: %(message)s")
logger = logging.getLogger(__name__)

# Environment variables
PROMETHEUS_URL = os.getenv("PROMETHEUS_URL", "http://prometheus:9090")
PROM_QUERY = os.getenv(
    "PROM_QUERY",
    'sum(clamp_min(kafka_consumergroup_lag{consumergroup="%s", topic="%s"}, 0))' % (
        os.getenv("GROUP_ID", "pdf-service"),
        os.getenv("TOPIC", "pdf-generation-requests")
    )
)
SERVICE_NAME = os.getenv("SERVICE_NAME", "mystack_pdf")

POLL_INTERVAL = int(os.getenv("POLL_INTERVAL_MS", "5000")) / 1000  # seconds
HIGH_LAG_THRESHOLD = int(os.getenv("HIGH_LAG_THRESHOLD", "6"))
LOW_LAG_THRESHOLD = int(os.getenv("LOW_LAG_THRESHOLD", "3"))
DOWNSCALE_STABLE_COUNT = int(os.getenv("DOWNSCALE_STABLE_COUNT", "1"))
IDLE_TIMEOUT_SECONDS = int(os.getenv("IDLE_TIMEOUT_SECONDS", "600"))  # 10 minutes
MAX_WAIT_TIME_SECONDS = int(os.getenv("MAX_WAIT_TIME_SECONDS", "300"))  # 5 minutes

MAX_REPLICAS = int(os.getenv("MAX_REPLICAS", "3"))
MIN_REPLICAS = 1  # Always keep at least 1 replica
SCALE_UP_REPLICAS = int(os.getenv("SCALE_UP_REPLICAS", "1"))

COOLDOWN_SECONDS = int(os.getenv("COOLDOWN_SECONDS", "10"))

# State
stable_low_count = 0
last_action_time = 0
previous_lag = 0  # Track previous lag value to detect decreases

# Docker client
docker_client = docker.from_env()


def get_total_lag():
    """Query Prometheus for Kafka lag."""
    try:
        logger.info(f"Querying Prometheus at {PROMETHEUS_URL} with query: {PROM_QUERY}")
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", params={"query": PROM_QUERY}, timeout=5)
        resp.raise_for_status()
        data = resp.json()
        logger.info(f"Prometheus response status: {data.get('status')}")
        if data["status"] != "success":
            logger.warning("Prometheus query failed: %s", data)
            return 0
        result = data["data"]["result"]
        if not result:
            logger.info("No results from Prometheus query")
            return 0
        lag_value = int(float(result[0]["value"][1]))
        logger.info(f"Retrieved lag value: {lag_value}")
        return lag_value
    except Exception as e:
        logger.error("Error querying Prometheus: %s", e)
        return 0


def get_current_replicas():
    """Get current replica count of the service."""
    try:
        logger.info(f"Getting current replicas for service: {SERVICE_NAME}")
        service = docker_client.services.get(SERVICE_NAME)
        spec = service.attrs["Spec"]
        replicas = spec["Mode"]["Replicated"]["Replicas"]
        logger.info(f"Current replicas for {SERVICE_NAME}: {replicas}")
        return replicas
    except Exception as e:
        logger.error(f"Failed to get current replicas for {SERVICE_NAME}: {e}")
        raise


def set_replicas(count):
    """Scale the service to the given replica count. Always keep at least 1 replica."""
    count = max(MIN_REPLICAS, min(MAX_REPLICAS, count))
    logger.info(f"Attempting to scale {SERVICE_NAME} to {count} replicas")
    service = docker_client.services.get(SERVICE_NAME)
    service.update(mode=docker.types.ServiceMode('replicated', replicas=count))
    logger.info(f"Successfully scaled {SERVICE_NAME} to {count} replicas")


def get_service_tasks():
    """Get all running tasks for the service with their creation timestamps."""
    try:
        service = docker_client.services.get(SERVICE_NAME)
        tasks = service.tasks()
        
        task_info = []
        for task in tasks:
            # Handle both task objects and dict responses
            if hasattr(task, 'attrs'):
                task_attrs = task.attrs
            elif isinstance(task, dict):
                task_attrs = task
            else:
                logger.warning(f"Unexpected task type: {type(task)}")
                continue
            
            # Only consider running tasks
            task_status = task_attrs.get('Status', {}).get('State', '')
            if task_status != 'running':
                logger.debug(f"Skipping task {task_attrs.get('ID', '')[:12]} with status: {task_status}")
                continue
            
            created_at = task_attrs.get('CreatedAt', '')
            task_id = task_attrs.get('ID', '')
            slot = task_attrs.get('Slot', 0)
            
            # Convert timestamp to seconds since epoch
            if created_at:
                # Docker timestamps are in format like "2024-01-01T12:00:00.123456789Z"
                import datetime
                try:
                    # Parse the timestamp
                    dt = datetime.datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    created_timestamp = dt.timestamp()
                except:
                    created_timestamp = 0
            else:
                created_timestamp = 0
            
            task_info.append({
                'task_id': task_id,
                'slot': slot,
                'created_at': created_at,
                'created_timestamp': created_timestamp,
                'status': task_status
            })
        
        # Sort by creation timestamp (newest first)
        task_info.sort(key=lambda x: x['created_timestamp'], reverse=True)
        
        logger.info(f"Found {len(task_info)} running tasks for {SERVICE_NAME}")
        for task in task_info:
            logger.info(f"Task {task['task_id'][:12]} (slot {task['slot']}) created at {task['created_at']} - Status: {task['status']}")
        
        return task_info
    except Exception as e:
        logger.error(f"Failed to get service tasks for {SERVICE_NAME}: {e}")
        return []


def scale_down_keeping_newest(target_replicas):
    """Simplified: directly scale to target replicas (no keep-newest logic)."""
    try:
        set_replicas(target_replicas)
        logger.info(f"Scaled to {target_replicas} replicas")
    except Exception as e:
        logger.error(f"Failed to scale to {target_replicas} replicas: {e}")


def check_active_work():
    """Check if there are any active tasks/jobs running in the containers."""
    try:
        # Query Prometheus for active work metrics
        # This could be based on:
        # - PDF generation in progress
        # - CPU/memory usage above idle threshold
        # - Active HTTP requests
        # - Custom metrics from the application
        
        # For now, let's check if there are any active HTTP requests or high CPU usage
        active_requests_query = 'sum(rate(http_requests_total{service="pdf-service"}[1m]))'
        cpu_usage_query = 'avg(rate(container_cpu_usage_seconds_total{name=~".*pdf.*"}[1m]))'
        
        # Check active requests
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", 
                          params={"query": active_requests_query}, timeout=5)
        if resp.status_code == 200:
            data = resp.json()
            if data["status"] == "success" and data["data"]["result"]:
                active_requests = float(data["data"]["result"][0]["value"][1])
                if active_requests > 0:
                    logger.info(f"Active requests detected: {active_requests}")
                    return True
        
        # Check CPU usage (if above 5%, consider it active work)
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", 
                          params={"query": cpu_usage_query}, timeout=5)
        if resp.status_code == 200:
            data = resp.json()
            if data["status"] == "success" and data["data"]["result"]:
                cpu_usage = float(data["data"]["result"][0]["value"][1])
                if cpu_usage > 0.05:  # 5% CPU threshold
                    logger.info(f"High CPU usage detected: {cpu_usage:.2%}")
                    return True
        
        # Alternative: Check if any containers are processing PDFs
        # This would require custom metrics from your PDF service
        pdf_processing_query = 'sum(pdf_generation_in_progress{service="pdf-service"})'
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", 
                          params={"query": pdf_processing_query}, timeout=5)
        if resp.status_code == 200:
            data = resp.json()
            if data["status"] == "success" and data["data"]["result"]:
                pdfs_processing = int(float(data["data"]["result"][0]["value"][1]))
                if pdfs_processing > 0:
                    logger.info(f"PDFs being processed: {pdfs_processing}")
                    return True
        
        logger.debug("No active work detected")
        return False
        
    except Exception as e:
        logger.warning(f"Failed to check active work: {e}")
        # If we can't check, assume there might be active work (safer)
        return True


def is_any_pdf_processing():
    """Return True if any container is currently processing PDFs (based on Prometheus metric)."""
    try:
        query = 'sum(clamp_min(pdf_generation_in_progress{service="pdf-service"}, 0))'
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", params={"query": query}, timeout=5)
        if resp.status_code == 200:
            data = resp.json()
            if data["status"] == "success" and data["data"]["result"]:
                val = float(data["data"]["result"][0]["value"][1])
                logger.info(f"PDFs in progress (sum): {val}")
                return val > 0
        return False
    except Exception as e:
        logger.warning(f"Failed to query pdf_generation_in_progress: {e}")
        # Be conservative if we can't check
        return True

def check_individual_container_activity():
    """Check which individual containers are active and which are idle."""
    try:
        # Get individual container metrics
        container_cpu_query = 'rate(container_cpu_usage_seconds_total{name=~".*pdf.*"}[1m]) by (container_name)'
        container_requests_query = 'rate(http_requests_total{service="pdf-service"}[1m]) by (container_name)'
        
        active_containers = []
        idle_containers = []
        
        # Check CPU usage per container
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", 
                          params={"query": container_cpu_query}, timeout=5)
        if resp.status_code == 200:
            data = resp.json()
            if data["status"] == "success" and data["data"]["result"]:
                for result in data["data"]["result"]:
                    container_name = result["metric"]["container_name"]
                    cpu_usage = float(result["value"][1])
                    
                    if cpu_usage > 0.05:  # 5% CPU threshold
                        active_containers.append(container_name)
                        logger.debug(f"Container {container_name} is active (CPU: {cpu_usage:.2%})")
                    else:
                        idle_containers.append(container_name)
                        logger.debug(f"Container {container_name} is idle (CPU: {cpu_usage:.2%})")
        
        # Check HTTP requests per container
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", 
                          params={"query": container_requests_query}, timeout=5)
        if resp.status_code == 200:
            data = resp.json()
            if data["status"] == "success" and data["data"]["result"]:
                for result in data["data"]["result"]:
                    container_name = result["metric"]["container_name"]
                    requests_rate = float(result["value"][1])
                    
                    if requests_rate > 0 and container_name not in active_containers:
                        active_containers.append(container_name)
                        logger.debug(f"Container {container_name} is active (requests: {requests_rate})")
        
        logger.info(f"Active containers: {len(active_containers)}, Idle containers: {len(idle_containers)}")
        return active_containers, idle_containers
        
    except Exception as e:
        logger.warning(f"Failed to check individual container activity: {e}")
        return [], []


def can_partially_scale_down():
    """Check if we can scale down some containers while keeping busy ones."""
    active_containers, idle_containers = check_individual_container_activity()
    
    # If we have idle containers and at least some active ones, we can scale down
    if idle_containers and active_containers:
        logger.info(f"Can scale down {len(idle_containers)} idle containers while keeping {len(active_containers)} active ones")
        return True, idle_containers
    
    # If all containers are idle, we can scale down to minimum
    elif idle_containers and not active_containers:
        logger.info(f"All containers are idle, can scale down to minimum")
        return True, idle_containers
    
    # If all containers are active, don't scale down
    else:
        logger.info("All containers are active, cannot scale down")
        return False, []


def can_safely_scale_down():
    """Check if it's safe to scale down (no active work)."""
    return not check_active_work()


def get_queue_item_age():
    """Get the age of the oldest item in the queue."""
    try:
        # Query for the oldest message timestamp in the queue
        # This requires Kafka metrics that track message timestamps
        oldest_message_query = f'''
        min(
            kafka_log_log_logendoffset{{topic="{os.getenv("TOPIC", "pdf-generation-requests")}"}} * 
            on(partition) group_left 
            kafka_log_log_logstartoffset{{topic="{os.getenv("TOPIC", "pdf-generation-requests")}"}}
        )
        '''
        
        # Alternative: Check consumer lag age
        # This gives us the age of the oldest unprocessed message
        consumer_lag_age_query = f'''
        max(
            kafka_consumergroup_lag_sum{{consumergroup="{os.getenv("GROUP_ID", "pdf-service")}", topic="{os.getenv("TOPIC", "pdf-generation-requests")}"}} * 
            on(partition) group_left 
            kafka_log_log_logendoffset{{topic="{os.getenv("TOPIC", "pdf-generation-requests")}"}}
        )
        '''
        
        # For now, let's use a simpler approach: check if lag has been stable for too long
        # This indicates items are waiting without being processed
        resp = requests.get(f"{PROMETHEUS_URL}/api/v1/query", 
                          params={"query": consumer_lag_age_query}, timeout=5)
        
        if resp.status_code == 200:
            data = resp.json()
            if data["status"] == "success" and data["data"]["result"]:
                # This is a simplified approach - in practice you'd need more sophisticated metrics
                # For now, we'll use the lag value as a proxy for wait time
                lag_value = float(data["data"]["result"][0]["value"][1])
                
                # If lag is high and stable, items are likely waiting
                if lag_value > 0:
                    logger.info(f"Queue has {lag_value} items waiting")
                    return lag_value
        
        # Fallback: check if there are any items in the queue at all
        total_lag = get_total_lag()
        if total_lag > 0:
            logger.info(f"Queue has {total_lag} items, checking if they're waiting too long")
            return total_lag
        
        return 0
        
    except Exception as e:
        logger.warning(f"Failed to get queue item age: {e}")
        return 0


def check_queue_wait_time():
    """Check if items in the queue have been waiting too long."""
    queue_age = get_queue_item_age()
    
    if queue_age > 0:
        logger.info(f"Queue items have been waiting (age: {queue_age})")
        # If we have items and they've been waiting, consider scaling up
        return True
    
    return False


def main():
    global stable_low_count, last_action_time, previous_lag

    logger.info(f"Starting autoscaler for {SERVICE_NAME}")
    logger.info(f"Prometheus URL: {PROMETHEUS_URL}")
    logger.info(f"Query: {PROM_QUERY}")
    logger.info(f"High lag threshold: {HIGH_LAG_THRESHOLD}")
    logger.info(f"Low lag threshold: {LOW_LAG_THRESHOLD}")
    logger.info(f"Idle timeout: {IDLE_TIMEOUT_SECONDS} seconds")
    logger.info(f"Max wait time: {MAX_WAIT_TIME_SECONDS} seconds")
    logger.info(f"Min replicas: {MIN_REPLICAS}, Max replicas: {MAX_REPLICAS}")

    while True:
        total_lag = get_total_lag()
        logger.info(f"Total lag: {total_lag}")

        now = time.time()
        if now - last_action_time < COOLDOWN_SECONDS:
            logger.debug(f"In cooldown period, skipping scaling decision")
            time.sleep(POLL_INTERVAL)
            continue

        try:
            current_replicas = get_current_replicas()
        except Exception as e:
            logger.error(f"Failed to get current replicas: {e}")
            time.sleep(POLL_INTERVAL)
            continue

        # Scale up due to high lag
        if total_lag > HIGH_LAG_THRESHOLD:
            logger.info(f"High lag detected ({total_lag} > {HIGH_LAG_THRESHOLD}), scaling up")
            desired = max(current_replicas + 1, SCALE_UP_REPLICAS)
            if desired > current_replicas:
                set_replicas(desired)
                last_action_time = now
                stable_low_count = 0
            time.sleep(POLL_INTERVAL)
            continue


        if LOW_LAG_THRESHOLD <= total_lag <= HIGH_LAG_THRESHOLD:
            logger.debug(f"In hysteresis zone ({LOW_LAG_THRESHOLD} <= {total_lag} <= {HIGH_LAG_THRESHOLD}), no action")
            stable_low_count = 0
            time.sleep(POLL_INTERVAL)
            continue

        # Scale down logic (simplified): if lag stays low for a few intervals, step down by 1
        if total_lag < LOW_LAG_THRESHOLD:
            stable_low_count += 1
            logger.info(f"Low lag detected ({total_lag} < {LOW_LAG_THRESHOLD}), stable count: {stable_low_count}/{DOWNSCALE_STABLE_COUNT}")
            if stable_low_count >= DOWNSCALE_STABLE_COUNT and current_replicas > MIN_REPLICAS:
                # Only scale down if no containers are actively processing PDFs
                if not is_any_pdf_processing():
                    desired = current_replicas - 1
                    set_replicas(desired)
                    last_action_time = now
                    stable_low_count = 0
                else:
                    logger.info("Active PDF processing detected, skipping scale down")
                    stable_low_count = 0

        # Update previous lag for next iteration
        previous_lag = total_lag
        logger.info(f"Previous lag: {previous_lag}")

        time.sleep(POLL_INTERVAL)


if __name__ == "__main__":
    main()
