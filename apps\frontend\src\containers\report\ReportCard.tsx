import { getInitials, getTypeIcon, isReportSubjectCompany } from "~/helpers";
import { <PERSON><PERSON><PERSON>, LuRedo } from "react-icons/lu";
import {
  SubjectCard,
  Separator,
  Avatar,
  Button,
  StandardList,
  ListItem,
  Text,
} from "@snap/design-system";
import { useNavigate, useParams } from "react-router";
import { MdOutlineDriveFileMove } from "react-icons/md";
import { ArrowRight, Download, Pencil, Trash, X } from "lucide-react";
import { BiMerge } from "react-icons/bi";
import { isErrorReport, isPendingReport } from "~/helpers/reportStatus.helper";
import { ReportModel } from "root/domain/entities/report.model";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { useEncryption } from "~/hooks/useEncryption";
import { toast } from "sonner";
import { REPORT_CONSTANTS } from "~/helpers/constants";
import { FaUsers } from "react-icons/fa6";
import { TbBuildings } from "react-icons/tb";
import { useDialogActions } from "~/store/dialogStore";
import { MoveReportToFolderDialog } from "./MoveReportToFolderDialog";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { RenameReportDialog } from "./RenameReportDialog";

export default function ReportCard({
  report,
  onAccess,
  onDownloadPDF,
  isDownloadingPDF = false,
}: {
  report: ReportModel;
  onAccess?: () => void | undefined;
  onRetry?: () => void | undefined;
  onDownloadPDF?: (reportId: string) => void;
  isDownloadingPDF?: boolean;
}) {
  const { folderId } = useParams<{ folderId?: string }>();
  const { newReportMutation, deleteReportMutation, renameReportMutation } = useReportCRUD(folderId);
  const { encryptData } = useEncryption();
  const navigate = useNavigate();
  const { openDialog, closeDialog } = useDialogActions();
  const listData = [...report.searchItems];

  const handleRetryReport = async () => {
    const searchArgs = report.searchArgs as Record<string, string[]>;
    const firstKey = Object.keys(searchArgs)[0];
    const valueString = searchArgs[firstKey]?.[0];

    try {
      const encryptedValue = await encryptData(report.searchArgs);

      if (!encryptedValue.data) {
        console.error("Error encrypting searchArgs:", encryptedValue.error);
        toast("Erro", {
          description: "Erro ao tentar refazer o relatório",
          action: {
            label: "Fechar",
            onClick: () => { },
          },
        });
        return;
      }

      newReportMutation.mutateAsync({
        report_type: report.reportType,
        report_input_value: valueString,
        report_input_encrypted: encryptedValue.data,
        user_reports_id: report.id,
        parent_folder_id: report.parentFolderId,
      });
    } catch (error) {
      console.error("Error processing newReportMutation:", error);
      toast("Erro", {
        description: "Erro ao tentar refazer o relatório",
        action: {
          label: "Fechar",
          onClick: () => { },
        },
      });
    }
  };

  const handleConfirmDelete = () => {
    openDialog({
      title: "Excluir relatório",
      icon: <Trash />,
      content: (
        <Text variant="body-md">
          Tem certeza que deseja excluir o relatório <span className="font-bold text-accent uppercase">{report.name}</span>?
        </Text>
      ),
      footer: (
        <div className="flex gap-3">
          <Button
            className="uppercase !bg-transparent"
            onClick={() => {
              closeDialog();
            }}
          >
            Cancelar
          </Button>
          <Button
            className="uppercase !bg-foreground !text-background !font-bold"
            icon={<Trash size={16} />}
            iconPosition="right"
            onClick={() => {
              deleteReportMutation.mutate(report.id);
            }}
          >
            Excluir
          </Button>
        </div>
      ),
      className: "max-w-lg",
    });
  };

  const handleMoveReport = () => {
    openDialog({
      title: "Mover relatório para pasta",
      icon: <MdOutlineDriveFileMove size={24} />,
      content: <MoveReportToFolderDialog.Content
        currentFolderId={report.parentFolderId || null}
        reportId={report.id}
      />,
      footer: (
        <MoveReportToFolderDialog.Footer />
      ),
      className: "max-w-4xl",
    });
  };

  const handleRenameReport = () => {
    openDialog({
      title: "Renomear Relatório",
      icon: <Pencil />,
      content: (
        <RenameReportDialog
          reportName={report.name}
          onCancel={() => closeDialog()}
          onConfirm={(newName) => {
            if (newName && newName !== report.name) {
              renameReportMutation.mutate({
                report_id: report.id,
                report_name: newName,
              });
            }
          }}
        />
      )
    });
  };

  const pendingHeader = (
    <div className="flex items-center bg-gray-700 justify-between gap-3 p-3 w-full">
      {getTypeIcon(report.reportType, 32)}
      <div>
        <Text className="uppercase">
          {`Relatório ${report.reportType}`}
        </Text>
      </div>
    </div>
  );

  const moveFolderMenuItem = {
    label: "mover para pasta",
    icon: <MdOutlineDriveFileMove className="size-4" />,
    onClick: handleMoveReport,
  };

  const mergeFolderMenuItem = {
    label: "combinar relatório",
    icon: <BiMerge className="size-4" />,
    onClick: () => alert("AÇÃO DE COMBINAR RELATÓRIO ESTÁ EM CONSTRUÇÃO"),
  };

  const renameFolderMenuItem = {
    label: "renomear relatório",
    icon: <Pencil className="size-4" />,
    onClick: handleRenameReport,
  };

  const downloadPDFMenuItem = {
    label: isDownloadingPDF ? "Gerando PDF..." : "Exportar PDF",
    icon: isDownloadingPDF ? (
      <AiOutlineLoading3Quarters size={16} className="animate-spin" />
    ) : (
      <Download className="size-4" />
    ),
    onClick: () => onDownloadPDF?.(report.id),
    disabled: !onDownloadPDF || isDownloadingPDF,
  };

  const deleteFolderMenuItem = {
    label: "Excluir relatório",
    icon: <Trash className="size-4" />,
    onClick: handleConfirmDelete,
  };

  const initials = getInitials(report.subjectName);
  const defaultIcon = <div className="size-3 bg-border" />;
  const redirectTo = `/report/${report.reportType}/${report.id}`;

  const isCompany = isReportSubjectCompany(
    report.reportType,
    report.subjectAge,
    report.subjectSex,
    report.subjectMotherName
  );

  if (report.subjectMotherName) {
    listData.unshift({
      label: "MÃE",
      value: report.subjectMotherName,
      icon: defaultIcon,
    });
  }

  if (report.subjectAge != null && report.subjectAge !== "") {
    const ageLabel = isCompany ? "DATA DE FUNDAÇÃO" : "IDADE";
    listData.push({
      label: ageLabel,
      value: report.subjectAge,
      icon: defaultIcon,
    });
  }

  if (report.subjectSex) {
    listData.push({
      label: "SEXO",
      value: report.subjectSex,
      icon: defaultIcon,
    });
  }

  if (report.subjectPersonCount) {
    listData.push({
      label: "PESSOAS ENCONTRADAS",
      value: report.subjectPersonCount,
      icon: defaultIcon,
    });
  }

  if (report.subjectCompanyCount) {
    listData.push({
      label: "EMPRESAS ENCONTRADAS",
      value: report.subjectCompanyCount,
      icon: defaultIcon,
    });
  }

  if (report.subjectNameRelated) {
    listData.unshift({
      label: "RELACIONADO",
      value: report.subjectNameRelated,
      icon: defaultIcon,
    });
  }

  const isMultipleSubjects = report.subjectName === REPORT_CONSTANTS.multiplos_registros_encontrados;
  const multipleIcon = report.subjectPersonCount ? <FaUsers className="size-5" /> : <TbBuildings className="size-5" />;

  const header = (
    <>
      <div
        className="flex items-center bg-neutral-700 justify-between gap-3 p-3 w-full cursor-pointer hover:bg-neutral-400 transition-colors duration-200 group"
        onClick={() => navigate(redirectTo)}
      >
        {getTypeIcon(report.reportType, 32)}
        <span className="uppercase line-clamp-2 text-ellipsis max-w-4/5 group-hover:underline">
          {report.name}
        </span>
      </div>
      <Separator />
      <div className="p-3">
        {
          !isMultipleSubjects ? (
            <Avatar
              name={report.subjectName}
              fallback={initials || "N/A"}
              size="md"
              textAlign="right"
              textClassName="text-lg uppercase"
            />
          ) : (
            <div className="flex items-center justify-between gap-2">
              <div className="border-border border-4 rounded-full p-3">
                {multipleIcon}
              </div>
              <Text variant="body-lg" className="uppercase text-right">
                {report.subjectName}
              </Text>
            </div>
          )
        }
      </div>
    </>
  );
  const content = (
    <StandardList withSeparator>
      {listData.map(({ label, value, icon, highlight }) => (
        <ListItem
          key={`${label}-${value}`}
          icon={icon}
          label={label}
          value={value}
          highlight={highlight}
          className="text-foreground"
          labelClassName="text-sm"
          valueClassName="text-sm"
        />
      ))}
    </StandardList>
  );
  const actions = (
    <Button
      size="sm"
      iconPosition="right"
      icon={<ArrowRight className="size-4" />}
      className="w-full"
      onClick={() => navigate(redirectTo)}
    >
      Acessar
    </Button>
  );
  const defaultMenu = [
    moveFolderMenuItem,
    mergeFolderMenuItem,
    renameFolderMenuItem,
    downloadPDFMenuItem,
    deleteFolderMenuItem,
  ];

  if (isPendingReport(report.raw)) {
    return (
      <SubjectCard
        headerClassName="p-0"
        header={
          pendingHeader
        }
        content={
          <div className="relative flex flex-col gap-2">
            <div className="relative flex flex-col justify-center items-center p-4">
              <LuClock className="w-12 h-12 text-brand-primary animate-spin" />
              <Text variant="body-md" align="center" className="mt-2">Gerando seu relatório</Text>
            </div>
            {content}
          </div>
        }
        footer={{
          createdAt: report.createdAt,
          updatedAt: report.modifiedAt,
        }}
        menu={[deleteFolderMenuItem]}
        menuWrapperClassName="top-0 right-0"
        menuClassName="border border-card"
        cardClassName="max-w-[282px]"
        markShadowClassName="!bg-transparent"
      />
    );
  }

  if (isErrorReport(report.raw)) {
    return (
      <SubjectCard
        headerClassName="p-0"
        header={
          pendingHeader
        }
        content={
          <div className="relative flex flex-col gap-2">
            <div className="relative flex flex-col justify-center items-center p-4">
              <X className="w-12 h-12 text-primary" />
              <Text variant="body-md" align="center" className="mt-2">Ocorreu um erro ao gerar o relatório</Text>
            </div>
            {content}
          </div>
        }
        footer={{
          createdAt: report.createdAt,
          updatedAt: report.modifiedAt,
        }}
        actions={
          <Button onClick={onAccess ?? handleRetryReport}>Refazer<LuRedo /> </Button>
        }
        menu={[deleteFolderMenuItem]}
        menuWrapperClassName="top-0 right-0"
        menuClassName="border border-card"
        cardClassName="max-w-[282px]"
        markShadowClassName="!bg-transparent"
      />
    );
  }

  return (
    <SubjectCard
      headerClassName="p-0"
      header={header}
      content={content}
      actions={actions}
      menu={defaultMenu}
      menuWrapperClassName="top-0 right-0"
      menuClassName="border border-card"
      cardClassName="max-w-[282px]"
      footerClassName="[&_p]:text-sm"
      menuTriggerType="click"
      border={false}
      footer={{
        createdAt: report.createdAt,
        updatedAt: report.modifiedAt,
      }}
      markShadowClassName="!bg-transparent"
    />
  );
}
