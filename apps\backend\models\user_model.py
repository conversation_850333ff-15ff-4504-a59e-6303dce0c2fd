from sqlalchemy import Column, BigInteger, Text, UUID, String, DateTime, Index, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func, text
from sqlalchemy.orm import relationship

from models.base import Base

class Users(Base):
    __tablename__ = 'users'
    __table_args__ = (
        Index("idx_users_report_types", "report_types", postgresql_using="gin"),
        Index("idx_users_api_key", "api_key"),
        {"schema": "public"}
    )

    user_id = Column(UUID, primary_key=True)
    image = Column(Text, nullable=True)
    name = Column(Text, nullable=True)
    credits = Column(BigInteger, nullable=True)
    email = Column(Text, nullable=True, index=True)
    last_login = Column(DateTime(timezone=True), nullable=True, server_default=func.timezone('UTC', func.now()))
    report_types = Column(JSONB, nullable=False, default=list, server_default='[]')
    salt = Column(Text, nullable=True)
    verifier = Column(JSONB, nullable=True)
    role = Column(Text, nullable=False, default='standalone')
    api_key = Column(String, nullable=True, index=True)
    credits_monthly = Column(BigInteger, nullable=True)
    next_reset_credits = Column(DateTime(timezone=True), nullable=True)
    date_accept_terms = Column(DateTime(timezone=True), nullable=True)
    eula_version = Column(String, ForeignKey('public.eula.version'), nullable=True)

    eula = relationship("Eula", back_populates="user")
    organizations = relationship("OrganizationUsers", back_populates="user")
    sent_invites = relationship("Invite", back_populates="user_sender")
    user_report_ledger = relationship("UserReportLedger", back_populates="user", cascade="all, delete-orphan")  # optional

    def __repr__(self):
        return f"<Users(user_id={self.user_id}, name={self.name}, email={self.email})>"
