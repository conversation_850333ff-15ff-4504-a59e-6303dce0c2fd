from sqlalchemy import Column,  DateTime, Text, String
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from models.base import Base

class Eula(Base):
    __tablename__ = 'eula'
    __table_args__ = (
        {"schema": "public"}
    )

    version = Column(String, primary_key=True, nullable=False, index=True)
    content = Column(Text, nullable=False)
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now(), index=True)
    valid_until = Column(DateTime(timezone=True), nullable=True, index=True)

    # Relationship with users who accepted this EULA version
    user = relationship("Users", back_populates="eula", lazy="dynamic")

    def __repr__(self):
        return f"<Eula(version={self.version}, valid_until={self.valid_until})>"