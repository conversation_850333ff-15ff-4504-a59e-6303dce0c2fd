import logging

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession


from services.base_service import BaseService


from models.eula_model import Eula

logger = logging.getLogger(__name__)


class EulaService(BaseService):
    def __init__(self, db: AsyncSession, user_id: str) -> None:
        super().__init__(db)
        self.user_id=user_id

    async def get_eula(self):
        logger.info(f"Fetching active EULA for user: {self.user_id}")
        
        try:
            query = select(Eula).where(Eula.valid_until.is_(None)).order_by(Eula.created_at.desc())
            logger.debug(f"Executing EULA query: {query}")
            
            result = await self.db.execute(query)
            eula = result.scalar()
            
            if eula is None:
                logger.warning(f"No active EULA found in database")
            else:
                logger.info(f"Found active EULA version: {eula.version}")
                
            return eula
            
        except Exception as e:
            logger.error(f"Error fetching EULA from database: {str(e)}")
            raise