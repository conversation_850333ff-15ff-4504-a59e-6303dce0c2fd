import logging
import asyncio
import time
logger = logging.getLogger(__name__)
from jose import jwt
from urllib.parse import quote
from fastapi import Request, Response
import httpx

from core.jwt_utils import verify_jwt, refresh_access_token
from core.config import settings
from exceptions.business_exceptions import Use<PERSON><PERSON><PERSON><PERSON><PERSON>enticated<PERSON><PERSON>r, Fail<PERSON>ef<PERSON>TokenError, TokenExchangeFailedError
from core.jwt_utils import TokenVerificationFailedError

_refresh_locks = {}
_refresh_cache = {}
_lock_creation_times = {}  # Track when locks were created for cleanup
_failure_cache = {}  # Track failed refresh attempts to prevent hammering

# Lock cleanup settings
LOCK_TTL_SECONDS = 3600  # 1 hour - locks older than this will be cleaned up
MAX_LOCKS_COUNT = 1000   # Maximum number of locks to keep in memory

# Cache cleanup settings
CACHE_TTL_SECONDS = 30   # Default cache TTL for tokens
MAX_CACHE_ENTRIES = 1000 # Maximum number of cache entries to keep

# Lock timeout settings
LOCK_ACQUIRE_TIMEOUT = 30  # Maximum seconds to wait for lock acquisition

# Failure cache settings
FAILURE_CACHE_TTL = 300  # 5 minutes - how long to remember failures
MAX_FAILURE_CACHE_ENTRIES = 500  # Maximum number of failure entries to keep

# Extended outage protection settings
EXTENDED_OUTAGE_THRESHOLD = 10  # After 10 failures, consider it an extended outage
EXTENDED_OUTAGE_GRACE_PERIOD = 3600  # 1 hour - how long to keep users connected during outage


def _cleanup_old_locks():
    """Clean up old locks to prevent memory leaks."""
    current_time = time.time()
    
    # Find locks to remove (older than TTL or if we have too many)
    locks_to_remove = []
    
    # Remove locks older than TTL
    for token, creation_time in _lock_creation_times.items():
        if current_time - creation_time > LOCK_TTL_SECONDS:
            locks_to_remove.append(token)
    
    # If we still have too many locks, remove the oldest ones
    if len(_refresh_locks) - len(locks_to_remove) > MAX_LOCKS_COUNT:
        # Sort by creation time and remove oldest
        sorted_locks = sorted(_lock_creation_times.items(), key=lambda x: x[1])
        additional_removals_needed = len(_refresh_locks) - len(locks_to_remove) - MAX_LOCKS_COUNT
        for token, _ in sorted_locks[:additional_removals_needed]:
            if token not in locks_to_remove:
                locks_to_remove.append(token)
    
    # Perform cleanup
    if locks_to_remove:
        logger.info("[_cleanup_old_locks] Cleaning up %s old locks. Total locks before cleanup: %s", 
                    len(locks_to_remove), len(_refresh_locks))
        
        for token in locks_to_remove:
            try:
                _refresh_locks.pop(token, None)
                _lock_creation_times.pop(token, None)
            except Exception as e:
                logger.warning("[_cleanup_old_locks] Failed to remove lock for token: %s", str(e))
        
        logger.info("[_cleanup_old_locks] Cleanup complete. Total locks after cleanup: %s", len(_refresh_locks))


def _cleanup_cache():
    """Clean up expired cache entries and enforce size limits."""
    current_time = time.time()
    
    # Find expired entries
    expired_entries = []
    for token, entry in _refresh_cache.items():
        if entry and len(entry) == 3:
            _, _, expire_at = entry
            if expire_at <= current_time:
                expired_entries.append(token)
    
    # Remove expired entries
    if expired_entries:
        logger.info("[_cleanup_cache] Removing %s expired cache entries", len(expired_entries))
        for token in expired_entries:
            _refresh_cache.pop(token, None)
    
    # If cache is still too large, remove oldest entries
    if len(_refresh_cache) > MAX_CACHE_ENTRIES:
        # Sort by expiration time and remove oldest
        sorted_entries = sorted(
            [(token, entry[2]) for token, entry in _refresh_cache.items() if entry and len(entry) == 3],
            key=lambda x: x[1]
        )
        entries_to_remove = len(_refresh_cache) - MAX_CACHE_ENTRIES
        
        logger.info("[_cleanup_cache] Cache size (%s) exceeds limit (%s). Removing %s oldest entries", 
                    len(_refresh_cache), MAX_CACHE_ENTRIES, entries_to_remove)
        
        for token, _ in sorted_entries[:entries_to_remove]:
            _refresh_cache.pop(token, None)
        
        logger.info("[_cleanup_cache] Cache cleanup complete. Final cache size: %s", len(_refresh_cache))


def _is_refresh_failing(refresh_token: str) -> bool:
    """Check if refresh token has failed recently to avoid hammering."""
    failure_entry = _failure_cache.get(refresh_token)
    if not failure_entry:
        return False
    
    failure_time, failure_count = failure_entry
    current_time = time.time()
    
    # Extended outage protection: if too many failures, assume network outage
    if failure_count >= EXTENDED_OUTAGE_THRESHOLD:
        time_since_first_failure = current_time - failure_time
        if time_since_first_failure < EXTENDED_OUTAGE_GRACE_PERIOD:
            logger.warning("[_is_refresh_failing] Extended outage detected (%s failures). Keeping user connected for %s more seconds.",
                          failure_count, EXTENDED_OUTAGE_GRACE_PERIOD - time_since_first_failure)
            return True  # Block refresh attempts but don't disconnect user
        else:
            # Grace period expired, clear the failure record to try again
            _failure_cache.pop(refresh_token, None)
            logger.info("[_is_refresh_failing] Extended outage grace period expired. Attempting refresh again.")
            return False
    
    # Normal failure handling: block after 3 failures for 5 minutes
    if current_time - failure_time < FAILURE_CACHE_TTL and failure_count >= 3:
        logger.warning("[_is_refresh_failing] Refresh token has failed %s times recently. Blocking refresh attempt for %s more seconds.",
                      failure_count, FAILURE_CACHE_TTL - (current_time - failure_time))
        return True
    
    # Clean up old failure entries
    if current_time - failure_time >= FAILURE_CACHE_TTL:
        _failure_cache.pop(refresh_token, None)
        logger.info("[_is_refresh_failing] Cleared old failure entry for token.")
    
    return False


def _record_refresh_failure(refresh_token: str):
    """Record a refresh failure to prevent repeated attempts."""
    current_time = time.time()
    
    if refresh_token in _failure_cache:
        failure_time, failure_count = _failure_cache[refresh_token]
        # If recent failure, increment count; otherwise reset
        if current_time - failure_time < FAILURE_CACHE_TTL:
            _failure_cache[refresh_token] = (current_time, failure_count + 1)
        else:
            _failure_cache[refresh_token] = (current_time, 1)
    else:
        _failure_cache[refresh_token] = (current_time, 1)
    
    # Cleanup if cache gets too large
    if len(_failure_cache) > MAX_FAILURE_CACHE_ENTRIES:
        # Remove oldest entries
        sorted_failures = sorted(_failure_cache.items(), key=lambda x: x[1][0])
        for old_token, _ in sorted_failures[:len(_failure_cache) - MAX_FAILURE_CACHE_ENTRIES]:
            _failure_cache.pop(old_token, None)
    
    failure_count = _failure_cache[refresh_token][1]
    logger.warning("[_record_refresh_failure] Recorded failure #%s for refresh token. Cache size: %s", 
                  failure_count, len(_failure_cache))


def _clear_refresh_failure(refresh_token: str):
    """Clear failure record when refresh succeeds."""
    if _failure_cache.pop(refresh_token, None):
        logger.info("[_clear_refresh_failure] Cleared failure record for successful refresh.")


async def _check_keycloak_connectivity() -> bool:
    """Quick health check to see if Keycloak is reachable."""
    try:
        
        async with httpx.AsyncClient(verify=settings.KEYCLOAK_VERIFY_SSL, timeout=5.0) as client:
            # Try to reach Keycloak's health/status endpoint
            health_url = f"{settings.KEYCLOAK_URL}/health"
            response = await client.get(health_url)
            is_healthy = response.status_code == 200
            logger.info("[_check_keycloak_connectivity] Keycloak health check - status: %s, healthy: %s", 
                       response.status_code, is_healthy)
            return is_healthy
    except Exception as e:
        logger.warning("[_check_keycloak_connectivity] Keycloak health check failed: %s", str(e))
        return False


def _get_refresh_lock(key: str) -> asyncio.Lock:
    """Get or create a lock for the given refresh token to prevent concurrent refresh attempts."""
    logger.info("[_get_refresh_lock] Requesting lock for refresh token. token_length: %s", len(key) if key else 0)
    
    # Clean up old locks before getting/creating new ones
    _cleanup_old_locks()
    
    lock = _refresh_locks.get(key)
    if lock is None:
        logger.info("[_get_refresh_lock] No existing lock found for this token. Creating new lock.")
        lock = asyncio.Lock()
        _refresh_locks[key] = lock
        _lock_creation_times[key] = time.time()
        logger.info("[_get_refresh_lock] New lock created and stored. Total locks in memory: %s", len(_refresh_locks))
    else:
        logger.info("[_get_refresh_lock] Found existing lock for this token. Reusing lock.")
    
    logger.info("[_get_refresh_lock] Returning lock for token. Lock object: %s", id(lock))
    return lock


def _get_cached_tokens(old_refresh_token: str):
    """Get cached tokens if they exist and are still valid."""
    logger.info("[_get_cached_tokens] Checking cache for refresh token. token_length: %s", len(old_refresh_token) if old_refresh_token else 0)
    
    entry = _refresh_cache.get(old_refresh_token)
    if not entry:
        logger.info("[_get_cached_tokens] No cached entry found for this refresh token.")
        return None
    
    access_token, refresh_token, expire_at = entry
    current_time = time.time()
    
    logger.info("[_get_cached_tokens] Found cached entry. expire_at: %s, current_time: %s, ttl_remaining: %s seconds", 
                expire_at, current_time, expire_at - current_time)
    
    if expire_at <= current_time:
        logger.info("[_get_cached_tokens] Cached tokens have expired. Removing from cache.")
        try:
            # Use pop() to avoid KeyError if another thread removed it first
            removed_entry = _refresh_cache.pop(old_refresh_token, None)
            if removed_entry:
                logger.info("[_get_cached_tokens] Successfully removed expired tokens from cache. Cache size: %s", len(_refresh_cache))
            else:
                logger.info("[_get_cached_tokens] Expired tokens already removed by another thread. Cache size: %s", len(_refresh_cache))
        except Exception as e:
            logger.warning("[_get_cached_tokens] Failed to remove expired tokens from cache: %s", str(e))
        return None
    
    logger.info("[_get_cached_tokens] Returning valid cached tokens. access_token_length: %s, refresh_token_length: %s",
                len(access_token) if access_token else 0, len(refresh_token) if refresh_token else 0)
    return access_token, refresh_token


def _set_cached_tokens(old_refresh_token: str, new_access_token: str, new_refresh_token: str, ttl_seconds: int = None):
    """Cache the refreshed tokens with a TTL to prevent unnecessary refresh calls."""
    if ttl_seconds is None:
        ttl_seconds = CACHE_TTL_SECONDS
        
    logger.info("[_set_cached_tokens] Caching new tokens. old_refresh_token_length: %s, ttl_seconds: %s", 
                len(old_refresh_token) if old_refresh_token else 0, ttl_seconds)
    
    # Clean up cache if it's getting too large
    _cleanup_cache()
    
    expire_at = time.time() + ttl_seconds
    _refresh_cache[old_refresh_token] = (
        new_access_token,
        new_refresh_token,
        expire_at,
    )
    
    logger.info("[_set_cached_tokens] Successfully cached tokens. expire_at: %s, cache_size: %s", 
                expire_at, len(_refresh_cache))
    logger.info("[_set_cached_tokens] Cached token details - new_access_token_length: %s, new_refresh_token_length: %s",
                len(new_access_token) if new_access_token else 0, len(new_refresh_token) if new_refresh_token else 0)


def _log_lock_and_cache_status():
    """Log current status of locks, cache, and failures for monitoring purposes."""
    current_time = time.time()
    
    # Count active locks
    active_locks = len(_refresh_locks)
    
    # Count valid cached entries
    valid_cache_entries = 0
    expired_cache_entries = 0
    
    for token, entry in _refresh_cache.items():
        if entry and len(entry) == 3:
            _, _, expire_at = entry
            if expire_at > current_time:
                valid_cache_entries += 1
            else:
                expired_cache_entries += 1
    
    # Count active failure entries
    active_failures = 0
    expired_failures = 0
    
    for token, (failure_time, failure_count) in _failure_cache.items():
        if current_time - failure_time < FAILURE_CACHE_TTL:
            active_failures += 1
        else:
            expired_failures += 1
    
    logger.info("[_log_lock_and_cache_status] Current status - Active locks: %s, Valid cache entries: %s, Expired cache entries: %s, Active failures: %s, Expired failures: %s", 
                active_locks, valid_cache_entries, expired_cache_entries, active_failures, expired_failures)
    
    return {
        "active_locks": active_locks,
        "valid_cache_entries": valid_cache_entries,
        "expired_cache_entries": expired_cache_entries,
        "total_cache_entries": len(_refresh_cache),
        "active_failures": active_failures,
        "expired_failures": expired_failures,
        "total_failure_entries": len(_failure_cache)
    }


def _log_request_details(request: Request, response: Response = None):
    """Log detailed request and response information for debugging."""
    logger.info("[_log_request_details] === REQUEST DETAILS ===")
    logger.info("[_log_request_details] Method: %s, URL: %s", request.method, str(request.url))
    logger.info("[_log_request_details] Client IP: %s, User Agent: %s", 
               request.client.host if request.client else "unknown",
               request.headers.get("user-agent", "unknown"))
    
    # Log all headers
    logger.info("[_log_request_details] Request headers:")
    for header_name, header_value in request.headers.items():
        if header_name.lower() in ["authorization", "cookie"]:
            # Log sensitive headers with length only
            logger.info("[_log_request_details]   %s: [LENGTH: %s]", header_name, len(header_value))
        else:
            logger.info("[_log_request_details]   %s: %s", header_name, header_value)
    
    # Log cookies
    all_cookies = dict(request.cookies)
    logger.info("[_log_request_details] Request cookies: %s", list(all_cookies.keys()))
    for cookie_name, cookie_value in all_cookies.items():
        logger.info("[_log_request_details]   %s: [LENGTH: %s]", cookie_name, len(cookie_value))
    
    if response:
        logger.info("[_log_request_details] === RESPONSE DETAILS ===")
        logger.info("[_log_request_details] Status code: %s", response.status_code)
        
        # Log response headers
        logger.info("[_log_request_details] Response headers:")
        for header_name, header_value in response.headers.items():
            if header_name.lower() == "set-cookie":
                # Log Set-Cookie headers with length only
                logger.info("[_log_request_details]   %s: [LENGTH: %s]", header_name, len(header_value))
            else:
                logger.info("[_log_request_details]   %s: %s", header_name, header_value)
        
        # Count Set-Cookie headers
        try:
            set_cookie_headers = [
                h for (k, h) in ((k.decode().lower(), v.decode()) for k, v in getattr(response, "raw_headers", []))
                if k == "set-cookie"
            ]
            logger.info("[_log_request_details] Total Set-Cookie headers: %s", len(set_cookie_headers))
        except Exception as header_err:
            logger.warning("[_log_request_details] Could not count Set-Cookie headers: %s", header_err)
    
    logger.info("[_log_request_details] === END DETAILS ===")


async def auth_guard(request: Request, response: Response) -> dict:
    """
    Authentication guard that verifies JWT tokens and handles token refresh with proper locking.
    
    This function implements a robust locking mechanism to prevent multiple concurrent
    token refresh attempts for the same refresh token. If one request fails to verify
    the JWT, it will acquire a lock and other requests will wait until the refresh
    is complete and new tokens are set in cookies.
    """
    logger.info("[auth_guard] Starting authentication guard check...")
    
    # Log detailed request information for debugging
    _log_request_details(request)
    
    # Log detailed request information including authorization header
    auth_header = request.headers.get("authorization") or request.headers.get("Authorization")
    logger.info("[auth_guard] Request details - method: %s, url: %s, client_ip: %s", 
                request.method, str(request.url), request.client.host if request.client else "unknown")
    logger.info("[auth_guard] Authorization header present: %s, header_length: %s", 
                bool(auth_header), len(auth_header) if auth_header else 0)
    if auth_header:
        if auth_header.lower().startswith("bearer "):
            bearer_token = auth_header.split(" ", 1)[1].strip()
            logger.info("[auth_guard] Bearer token found - token_length: %s, token_preview: %s...", 
                        len(bearer_token), bearer_token[:20] if len(bearer_token) > 20 else bearer_token)
        else:
            logger.info("[auth_guard] Authorization header format: %s", auth_header[:50] + "..." if len(auth_header) > 50 else auth_header)
    
    # Log all cookies present
    all_cookies = dict(request.cookies)
    logger.info("[auth_guard] All cookies present: %s", list(all_cookies.keys()))
    if "access_token" in all_cookies:
        logger.info("[auth_guard] access_token cookie - length: %s, preview: %s...", 
                    len(all_cookies["access_token"]), all_cookies["access_token"][:20])
    if "refresh_token" in all_cookies:
        logger.info("[auth_guard] refresh_token cookie - length: %s, preview: %s...", 
                    len(all_cookies["refresh_token"]), all_cookies["refresh_token"][:20])
    
    # Extract tokens from cookies
    access_token = request.cookies.get("access_token")
    refresh_token = request.cookies.get("refresh_token")

    if not access_token or not refresh_token:
        logger.error("[auth_guard] Missing access_token or refresh_token in cookies")
        raise UserNotAuthenticatedError()

    logger.info("[auth_guard] Found tokens in cookies. access_token length: %s, refresh_token length: %s", 
                len(access_token) if access_token else 0, 
                len(refresh_token) if refresh_token else 0)

    try:
        # First attempt: try to verify the current access token
        logger.info("[auth_guard] Attempting to verify current access token...")
        return await verify_jwt(access_token)
        
    except TokenVerificationFailedError as e:
        logger.warning("[auth_guard] Access token verification failed: %s", str(e))
        logger.info("[auth_guard] Token expired or invalid. Initiating token refresh process with locking...")
        
        # Get lock for this specific refresh token to prevent concurrent refresh attempts
        lock = _get_refresh_lock(refresh_token)
        
        logger.info("[auth_guard] About to acquire lock for refresh token. Lock object: %s", id(lock))
        
        try:
            # Use asyncio.wait_for to add timeout to lock acquisition  
            await asyncio.wait_for(lock.acquire(), timeout=LOCK_ACQUIRE_TIMEOUT)
            logger.info("[auth_guard] Successfully acquired refresh lock for token. Checking cache for existing refresh...")
            
            try:
                # Check if refresh has been failing recently to avoid hammering
                if _is_refresh_failing(refresh_token):
                    failure_entry = _failure_cache.get(refresh_token)
                    if failure_entry:
                        _, failure_count = failure_entry
                        
                        # Extended outage: keep user connected with existing token
                        if failure_count >= EXTENDED_OUTAGE_THRESHOLD:
                            logger.warning("[auth_guard] Extended network outage detected. Keeping user connected with existing access token.")
                            logger.warning("[auth_guard] User will remain authenticated until network connectivity is restored.")
                            # Return the current JWT claims without refreshing
                            try:
                                # Try to use the current access token even if it might be expired
                                access_token = request.cookies.get("access_token")
                                if access_token:
                                    # Parse without verification for claims (risky but necessary during outage)
                                    
                                    unverified_claims = jwt.get_unverified_claims(access_token)
                                    logger.info("[auth_guard] Using unverified token claims during extended outage.")
                                    return unverified_claims
                            except Exception as e:
                                logger.error("[auth_guard] Could not extract claims from access token during outage: %s", str(e))
                            
                            # Fallback: create minimal user context
                            logger.warning("[auth_guard] Creating minimal user context during extended outage.")
                            return {
                                "sub": "outage_user",
                                "preferred_username": "user_during_outage",
                                "email": "<EMAIL>",
                                "name": "User (Network Outage)",
                                "outage_mode": True
                            }
                        else:
                            # Normal failure: block refresh attempt
                            logger.error("[auth_guard] Refresh token has failed multiple times recently. Blocking refresh attempt.")
                            logger.error("[auth_guard] Consider checking network connectivity or Keycloak service status.")
                            raise FailRefreshTokenError()
                    else:
                        logger.error("[auth_guard] Refresh blocking detected but no failure entry found.")
                        raise FailRefreshTokenError()
                
                # Check if another request has already refreshed the tokens using the current refresh token
                # Also check if we have any cached tokens that might be valid (for rotated refresh tokens)
                cached = _get_cached_tokens(refresh_token)
                if not cached:
                    # Try to find any cached tokens that might be valid (for cases where refresh token was rotated)
                    logger.info("[auth_guard] No direct cache hit for refresh token. Checking for any valid cached tokens...")
                    # This is a fallback - in a production system, you'd want to track refresh token rotation more carefully
                    for cached_refresh_token, cached_entry in _refresh_cache.items():
                        if cached_entry and len(cached_entry) == 3:
                            cached_access_token, cached_refresh_token_value, cached_expire_at = cached_entry
                            if cached_expire_at > time.time():
                                logger.info("[auth_guard] Found valid cached tokens from previous refresh. Using cached tokens.")
                                cached = (cached_access_token, cached_refresh_token_value)
                                break
                
                if cached:
                    new_access_token, new_refresh_token = cached
                    logger.info("[auth_guard] Found cached refreshed tokens from another request. Using cached tokens.")
                    logger.info("[auth_guard] Cached tokens - access_token length: %s, refresh_token length: %s",
                              len(new_access_token) if new_access_token else 0,
                              len(new_refresh_token) if new_refresh_token else 0)
                else:
                    logger.info("[auth_guard] No cached tokens found. Performing fresh token refresh...")
                    logger.info("[auth_guard] Refresh process details - refresh_token_length: %s, client_id: %s, keycloak_url: %s", 
                               len(refresh_token) if refresh_token else 0, settings.CLIENT_ID_KEYCLOAK, settings.KEYCLOAK_URL)
                    
                    try:
                        # Perform the actual token refresh
                        logger.info("[auth_guard] Calling refresh_access_token function...")
                        new_access_token, new_refresh_token = await refresh_access_token(refresh_token)
                        
                        logger.info("[auth_guard] Token refresh successful. New tokens obtained.")
                        logger.info("[auth_guard] New tokens - access_token length: %s, refresh_token length: %s",
                                  len(new_access_token) if new_access_token else 0,
                                  len(new_refresh_token) if new_refresh_token else 0)
                        
                        # Cache the new tokens for other concurrent requests using the NEW refresh token as key
                        logger.info("[auth_guard] Caching new tokens for concurrent requests...")
                        _set_cached_tokens(new_refresh_token, new_access_token, new_refresh_token)
                        logger.info("[auth_guard] Cached new tokens for concurrent requests using new refresh token as cache key")
                        
                        # Clear the old cache entry to prevent confusion with rotated refresh tokens
                        if refresh_token in _refresh_cache:
                            _refresh_cache.pop(refresh_token, None)
                            logger.info("[auth_guard] Cleared old cache entry for rotated refresh token")
                        
                        # Clear any failure records since refresh succeeded
                        logger.info("[auth_guard] Clearing failure records due to successful refresh...")
                        _clear_refresh_failure(refresh_token)
                        
                    except Exception as refresh_error:
                        logger.error("[auth_guard] Token refresh failed: %s", str(refresh_error))
                        logger.error("[auth_guard] Refresh process encountered an error. Recording failure and raising FailRefreshTokenError")
                        logger.error("[auth_guard] Error details - exception_type: %s, exception_message: %s", 
                                   type(refresh_error).__name__, str(refresh_error))
                        
                        # Record the failure to prevent hammering
                        logger.info("[auth_guard] Recording refresh failure to prevent hammering...")
                        _record_refresh_failure(refresh_token)
                        raise FailRefreshTokenError()

                # Set cookies with the new tokens
                logger.info("[auth_guard] Setting new tokens in cookies...")
                logger.info("[auth_guard] Cookie setting details - new_access_token_length: %s, new_refresh_token_length: %s, frontend_url: %s", 
                           len(new_access_token) if new_access_token else 0, 
                           len(new_refresh_token) if new_refresh_token else 0,
                           settings.FRONTEND_REDIRECT_URL)
                
                # Determine cookie security settings based on environment
                if "localhost" in settings.FRONTEND_REDIRECT_URL:
                    is_secure = True
                    same_site = "none"
                else:
                    is_secure = False
                    same_site = "lax"

                logger.info("[auth_guard] Cookie settings - secure: %s, same_site: %s, frontend_url: %s", is_secure, same_site, settings.FRONTEND_REDIRECT_URL)

                # Set the new access token cookie
                try:
                    response.set_cookie(
                        "access_token",
                        new_access_token,
                        httponly=True,
                        secure=is_secure,
                        samesite=same_site,
                        path="/",
                    )
                    logger.info("[auth_guard] Successfully set access_token cookie with settings - httponly: True, secure: %s, samesite: %s, path: /", is_secure, same_site)
                except Exception as cookie_error:
                    logger.error("[auth_guard] Failed to set access_token cookie: %s", str(cookie_error))
                    logger.error("[auth_guard] Cookie setting failed - access_token_length: %s, secure: %s, same_site: %s", 
                               len(new_access_token) if new_access_token else 0, is_secure, same_site)
                    raise FailRefreshTokenError()

                # Set the new refresh token cookie
                try:
                    response.set_cookie(
                        "refresh_token",
                        new_refresh_token,
                        httponly=True,
                        secure=is_secure,
                        samesite=same_site,
                        path="/",
                    )
                    logger.info("[auth_guard] Successfully set refresh_token cookie with settings - httponly: True, secure: %s, samesite: %s, path: /", is_secure, same_site)
                except Exception as cookie_error:
                    logger.error("[auth_guard] Failed to set refresh_token cookie: %s", str(cookie_error))
                    logger.error("[auth_guard] Cookie setting failed - refresh_token_length: %s, secure: %s, same_site: %s", 
                               len(new_refresh_token) if new_refresh_token else 0, is_secure, same_site)
                    raise FailRefreshTokenError()

                # Log cookie header count for debugging
                try:
                    set_cookie_headers = [
                        h for (k, h) in ((k.decode().lower(), v.decode()) for k, v in getattr(response, "raw_headers", []))
                        if k == "set-cookie"
                    ]
                    logger.info("[auth_guard] Total Set-Cookie headers after setting tokens: %s", len(set_cookie_headers))
                    
                    # Log details of each Set-Cookie header for debugging
                    for i, header in enumerate(set_cookie_headers):
                        logger.info("[auth_guard] Set-Cookie header %s: %s", i+1, header[:200] + "..." if len(header) > 200 else header)
                        
                except Exception as header_err:
                    logger.warning("[auth_guard] Could not count Set-Cookie headers: %s", header_err)
                    logger.warning("[auth_guard] Response object type: %s, has raw_headers: %s", 
                                 type(response), hasattr(response, "raw_headers"))

                # Verify the new access token before returning
                logger.info("[auth_guard] Verifying newly refreshed access token...")
                try:
                    user_data = await verify_jwt(new_access_token)
                    logger.info("[auth_guard] New access token verification successful. Authentication complete.")
                    
                    # Log final response details
                    _log_request_details(request, response)
                    
                    return user_data
                except TokenVerificationFailedError as verify_error:
                    logger.error("[auth_guard] New access token verification failed: %s", str(verify_error))
                    logger.error("[auth_guard] This indicates a critical issue with the refresh process")
                    raise FailRefreshTokenError()
                    
            finally:
                # Always release the lock
                lock.release()
                logger.info("[auth_guard] Released refresh lock. Lock object: %s", id(lock))
                
                # Log current status for monitoring
                _log_lock_and_cache_status()
        
        except asyncio.TimeoutError:
            logger.error("[auth_guard] Timeout waiting for refresh lock acquisition after %s seconds", LOCK_ACQUIRE_TIMEOUT)
            raise FailRefreshTokenError()
        except Exception as e:
            logger.error("[auth_guard] Unexpected error during token refresh: %s", str(e))
            raise FailRefreshTokenError()



async def exchange_code_for_tokens(code: str, frontend):
    logger.info("[exchange_code_for_tokens] Received code: %s", code)
    token_endpoint = "%s/realms/%s/protocol/openid-connect/token" % (settings.KEYCLOAK_URL, settings.REALM_NAME)
    logger.info("[exchange_code_for_tokens] Token endpoint URL: %s", token_endpoint)

    logger.info("[exchange_code_for_tokens] Received frontend: %s", frontend)
    # frontend="http://localhost"
    redirect_uri = f"{settings.REDIRECT_URI_KEYCLOAK}?frontend={quote(frontend)}"
    # redirect_uri = f"{settings.REDIRECT_URI_KEYCLOAK}"
    data = {
        "grant_type": "authorization_code",
        "code": code,
        "redirect_uri": redirect_uri,
        "client_id": settings.CLIENT_ID_KEYCLOAK,
        "client_secret": settings.CLIENT_SECRET_KEYCLOAK,
    }

    max_attempts = 3
    for attempt in range(1, max_attempts + 1):
        try:
            async with httpx.AsyncClient(verify=settings.KEYCLOAK_VERIFY_SSL, timeout=10.0) as client:
                logger.info("[exchange_code_for_tokens] Sending POST request to Keycloak... (attempt %s/%s)", attempt, max_attempts)
                response = await client.post(
                    token_endpoint,
                    data=data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
            break
        except httpx.RequestError as e:
            if attempt == max_attempts:
                logger.error("[exchange_code_for_tokens] Network error on last attempt: %s", e)
                raise TokenExchangeFailedError(0, str(e))
            backoff = 0.5 * (2 ** (attempt - 1))
            logger.warning("[exchange_code_for_tokens] Network error (attempt %s/%s): %s. Retrying in %.1fs", attempt, max_attempts, e, backoff)
            await asyncio.sleep(backoff)

    logger.info("[exchange_code_for_tokens] Response status code: %s", response.status_code)

    if response.status_code != 200:
        try:
            error_text = response.text
            logger.error("[exchange_code_for_tokens] Error response from Keycloak: %s", error_text)
        except Exception as e:
            logger.error("[exchange_code_for_tokens] Error reading response: %s", e)
        raise TokenExchangeFailedError(response.status_code, response.text)

    logger.info("[exchange_code_for_tokens] Successfully received tokens.")
    return response.json()

