{"vinculos": {"socio": "É Sócio na empresa", "parente IRMA(O)": "É irmão(a) de", "vinculo empregaticio": "Possui v<PERSON><PERSON>lo empregat<PERSON>", "outros contatos": "É um contato de"}, "vinculos_from_empresa": {"socio": "possui como Sócio", "parente IRMA(O)": "tem como parente (irmão/irmã)", "vinculo empregaticio": "possui vínculo empregatício com", "outros contatos": "tem como contato"}, "vinculos_from_pessoa": {"socio": "é Sócio na empresa", "parente IRMA(O)": "é irmão(a) de", "vinculo empregaticio": "possui vínculo empregatício na", "outros contatos": "é um contato de"}, "prefixo_semantico": {"1": "que", "2": "que também", "3": "que", "4": "que também", "5": "que", "6": "que também", "7": "que", "8": "que também", "9": "que", "10": "que também"}}