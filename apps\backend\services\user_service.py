import logging
from datetime import datetime, timezone
import pytz
from sqlalchemy import select, update, text, extract, func
from sqlalchemy.ext.asyncio import AsyncSession
import json
from jose import jwt
from keycloak_client import get_keycloak_admin
from keycloak import KeycloakGetError


from utils.utils import create_salt

from database.db import get_database_async_engine

from models.user_model import Users
from models.report_model import UserReports
from models.eula_model import Eula

from schemas.report_schema import InsertVerifier
from schemas.user_schema import Investigator<PERSON><PERSON>, OtherUser, AdministratorUser

from core.constants import KeycloakGroup, Roles, ReportTypes, Fields, UserFields, RolesDb

from services.base_service import BaseService
from services.credits_service import CreditsService
from exceptions.business_exceptions import UserDatabaseOperationError, UserEditPermissionDeniedError
from exceptions.base_exceptions import InternalServerError, ValidationError

# Setup logger
logger = logging.getLogger(__name__)


class UserStandaloneService(BaseService):
    def __init__(self, db: AsyncSession , user_id: str) -> None:
        super().__init__(db)
        self.user_id=user_id
        self.keycloak_admin=get_keycloak_admin()

    logger = logging.getLogger(__name__)


    def remove_user_from_all_groups(self):
        # List all groups assigned to the user
        user_groups = self.keycloak_admin.get_user_groups(self.user_id)
        logger.info(f"[remove_user_from_all_groups] User {self.user_id} is currently in {len(user_groups)} groups")
        
        for group in user_groups:
            group_name = group.get('name', 'Unknown')
            group_id = group['id']
            logger.info(f"[remove_user_from_all_groups] Removing user {self.user_id} from group '{group_name}' (ID: {group_id})")
            self.keycloak_admin.group_user_remove(
                user_id=self.user_id,
                group_id=group_id
            )
            logger.info(f"[remove_user_from_all_groups] Successfully removed user {self.user_id} from group '{group_name}'")
        
        if not user_groups:
            logger.info(f"[remove_user_from_all_groups] User {self.user_id} was not in any groups")


    def remove_user_from_all_roles(self):
        # Remove all realm roles
        logger.info(f"[remove_user_from_all_roles] Starting to remove realm roles for user {self.user_id}")
        
        realm_roles = self.keycloak_admin.get_realm_roles_of_user(self.user_id)
        logger.info(f"[remove_user_from_all_roles] User {self.user_id} has {len(realm_roles)} realm roles")
        
        total_roles_removed = 0
        if realm_roles:
            logger.info(f"[remove_user_from_all_roles] Found realm roles: {[role.get('name', 'Unknown') for role in realm_roles]}")
            for role in realm_roles:
                role_name = role.get('name', 'Unknown')
                logger.info(f"[remove_user_from_all_roles] Removing realm role '{role_name}' from user {self.user_id}")
            
            self.keycloak_admin.delete_realm_roles_of_user(self.user_id, realm_roles)
            total_roles_removed = len(realm_roles)
            logger.info(f"[remove_user_from_all_roles] Successfully removed {total_roles_removed} realm roles from user {self.user_id}")
        else:
            logger.info(f"[remove_user_from_all_roles] User {self.user_id} has no realm roles to remove")
    
        logger.info(f"[remove_user_from_all_roles] Total realm roles removed from user {self.user_id}: {total_roles_removed}")
        if total_roles_removed == 0:
            logger.info(f"[remove_user_from_all_roles] User {self.user_id} had no realm roles to remove")

        clients = self.keycloak_admin.get_clients()
        for client in clients:
            client_id = client['id']
            client_roles = self.keycloak_admin.get_client_roles_of_user(self.user_id, client_id)
            if client_roles:
                self.keycloak_admin.delete_client_roles_of_user(self.user_id, client_id, client_roles)


    def add_keycloak_user_to_group(self,
                                group_name: str
                                    ):
        """
        Adds a Keycloak user to a group based on organization ID, creating the group if it doesn't exist.
        """
        logger.info(f"[add_keycloak_user_to_group] Starting to add user {self.user_id} to group '{group_name}'")
        
        if group_name:
            logger.info(f"[add_keycloak_user_to_group] Group name provided: '{group_name}'")
            logger.info(f"[add_keycloak_user_to_group] Adding Keycloak user {self.user_id} to group '{group_name}'...")
            try:
                logger.info(f"[add_keycloak_user_to_group] Searching for groups with name '{group_name}'")
                groups = self.keycloak_admin.get_groups({"search": group_name})
                logger.info(f"[add_keycloak_user_to_group] Found {len(groups)}  groups {groups} matching name '{group_name}'")
                
                group_id = None
                if groups:
                    group_id = groups[0]['id']
                    group_details = groups[0]
                    logger.info(f"[add_keycloak_user_to_group] Selected group: ID={group_id}, Name='{group_details.get('name', 'Unknown')}', Path='{group_details.get('path', 'Unknown')}'")
                else:
                    logger.warning(f"[add_keycloak_user_to_group] No groups found with name '{group_name}'")

                if group_id:
                    logger.info(f"[add_keycloak_user_to_group] Attempting to add user {self.user_id} to group ID {group_id}")
                    self.keycloak_admin.group_user_add(
                        user_id=self.user_id,
                        group_id=group_id
                    )
                    logger.info(f"[add_keycloak_user_to_group] Successfully added Keycloak user {self.user_id} to group '{group_name}' (ID: {group_id})")
                else:
                    logger.error(f"[add_keycloak_user_to_group] Cannot add user {self.user_id} to group '{group_name}' - group not found")
                    raise ValidationError(detail=f"Group '{group_name}' not found in Keycloak")
                    
            except KeycloakGetError as e:
                logger.error(f"[add_keycloak_user_to_group] KeycloakGetError while adding user {self.user_id} to group '{group_name}': {e}")
                raise InternalServerError(detail=str(e))
            except Exception as e:
                logger.error(f"[add_keycloak_user_to_group] Error adding Keycloak user {self.user_id} to group '{group_name}': {e}", exc_info=True)
                raise InternalServerError(detail=str(e))
        else:
            logger.warning(f"[add_keycloak_user_to_group] No group name provided for user {self.user_id}")
            raise ValidationError(detail="Group name cannot be empty")


    def add_keycloak_user_to_role(self, role_name: str):
        """
        Assign a realm role to a Keycloak user.
        """
        try:
            role = self.keycloak_admin.get_realm_role(role_name)
            if role:
                self.keycloak_admin.assign_realm_roles(user_id=self.user_id, roles=[role])
                logger.info(f"Keycloak user {self.user_id} added to realm role '{role_name}'.")
        except Exception as e:
            logger.error(f"[KeycloakService] Error adding Keycloak user {self.user_id} to role '{role_name}': {e}", exc_info=True)
            raise InternalServerError(detail=str(e))


    async def update_user_role_in_db(self, 
                                     new_role: str, credits_monthly: int, 
                                     credits_remaining: int = None, 
                                     report_types: list = None, 
                                     next_reset_credits: datetime = None):
        
        logger.info(f"[update_user_role_in_db] Starting role update for user {self.user_id} to role {new_role}")
        logger.debug(f"[update_user_role_in_db] Additional parameters -  report_types: {report_types}")

        try:
            report_type = report_types

            if credits_remaining is None:
                credits_remaining=credits_monthly

            data_to_update = {
                Fields.role: new_role,
                Fields.report_types: report_type,
                Fields.total_credits: credits_remaining,
                UserFields.credits_monthly: credits_monthly,
                UserFields.next_reset_credits: next_reset_credits

            }

            logger.debug(f"[update_user_role_in_db] Update data prepared: {data_to_update}")

            query = (
                update(Users)
                .where(Users.user_id == self.user_id)
                .values(**data_to_update)
            )
            await self.db.execute(query)
            await self.db.commit()
            logger.info(f"[update_user_role_in_db] Successfully updated user {self.user_id} role to {new_role}")
        except Exception as e:
            await self.db.rollback()
            logger.error(f"[update_user_role_in_db] Error updating user {self.user_id} role: {str(e)}", exc_info=True)
            raise UserDatabaseOperationError(str(e))


    async def get_user_data(self, email: str = None) -> dict:
        """
        Get the data from user table.
            
        Returns:
            dict: The user data
            
        Raises:
            HTTPException: If user is not found
        """
        logger.info("[UserService] Retrieving user info for user_id: %s", self.user_id)
        try:

            if email is None:
                query = (
                    select(Users)
                    .where(
                        Users.user_id == self.user_id,
                    )
                )
            else:
                query = (
                    select(Users)
                    .where(
                        Users.email == email,
                    )
                )
            
            logger.debug("[UserService] Executing user query for organization: %s", self.user_id)
            result = await self.db.execute(query)
            user = result.scalars().first()          

            logger.info("[UserService] Found user: %s for user_id: %s", user  , self.user_id)
            return user  
            
        except Exception as e:
            logger.error(
                "[UserService] Failed to get user_id for user %s. Error: %s",
                self.user_id,
                str(e),
                exc_info=True
            )
            raise UserDatabaseOperationError(str(e))


    async def count_reports_by_month_year(self, month, year):
        try:
            conditions = [
                UserReports.user_id == self.user_id,
                extract('year', UserReports.created_at) == year,
                extract('month', UserReports.created_at) == month
            ]
            #A FUNCAO ABAIXO É PARA FILTRAR AQUELES RELATORIOS QUE DERAM ERRO NA CONTAGEM DE MES/ANO:
            # conditions = [
            #     UserReports.user_id == self.user_id,
            #     extract('year', UserReports.created_at) == year,
            #     extract('month', UserReports.created_at) == month,
            #     UserReports.report_status.has_key('status_report'),  # só onde existe a chave
            #     UserReports.report_status["status_report"].astext != SummaryReportStatus.error
            # ]

            if self.organization_id is not None:
                conditions.append(UserReports.organization_id == self.organization_id)

            # Create query for each report_type
            report_types = [ReportTypes.cpf, ReportTypes.cnpj, ReportTypes.email, ReportTypes.telefone]
            counts = {}

            for report_type in report_types:
                query = select(func.count()).where(
                    *conditions,
                    UserReports.report_type == report_type
                )
                result = await self.db.execute(query)
                counts[report_type] = result.scalar()

            return counts

        except Exception as e:
            logger.error(
                "[UserService] Error counting reports for user_id %s for month %s and year %s: %s",
                self.user_id, month, year, str(e), exc_info=True
            )
            raise UserDatabaseOperationError(str(e))


    async def insert_verifier_handler(self, body: InsertVerifier):
        logger.info("[insert_verifier_handler][user(%s)] Inserting verifier", self.user_id)

        try:
            await self.db.execute(
                update(Users)
                .where(Users.user_id == self.user_id)
                .values({Fields.verifier: body.verifier})
            )
            await self.db.commit()
            logger.info("[insert_verifier_on_db][user(%s)] Verifier inserted successfully.", self.user_id)
            return {"verifier": body.verifier, "status": "só sucesso"}
        except Exception as e:
            logger.error("[insert_verifier_on_db][user(%s)] Error: %s", self.user_id, e)
            raise UserDatabaseOperationError(str(e))
        

    async def update_user(self):
        
        logger.info(f"[Standalone.update_user] Starting update for standalone user {self.user_id}")
        try:
            logger.info(f"[Standalone.update_user] Removing user from all groups")
            self.remove_user_from_all_groups()
            self.remove_user_from_all_roles()

            self.add_keycloak_user_to_group(KeycloakGroup.UserStandAlone)
            self.add_keycloak_user_to_role(Roles.standAlone)

            user_data = await self.get_user_data()
            report_types = []

            if user_data.api_key:
                report_types = [ReportTypes.cpf, ReportTypes.cnpj, ReportTypes.combinado,
                                    ReportTypes.email, ReportTypes.telefone, ReportTypes.relacao]

            logger.info(f"[Standalone.update_user] Updating user role in database")
            await self.update_user_role_in_db(new_role=RolesDb.standAlone, 
                                              report_types=report_types,
                                              credits_remaining=None, 
                                              credits_monthly=None,
                                              next_reset_credits=None)

            logger.info(f"[Standalone.update_user] Successfully updated standalone user {self.user_id}")
        except Exception as e:
            logger.error(f"[Standalone.update_user] Error updating standalone user {self.user_id}: {str(e)}", exc_info=True)
            raise UserDatabaseOperationError(str(e))


    async def update_user_accept_terms(self):
        logger.info(f"[Standalone.update_user_accept_terms] Starting update for user {self.user_id}")
        try:

            eula_version = await self.db.execute(
                select(Eula).where(Eula.valid_until.is_(None)).order_by(Eula.created_at.desc())
            )
            eula_version = eula_version.scalar()

            await self.db.execute(
                update(Users)
                .where(Users.user_id == self.user_id)
                .values({Users.date_accept_terms: datetime.now(timezone.utc),
                         Users.eula_version: eula_version.version})
            )

            await self.db.commit()
            logger.info(f"[Standalone.update_user_accept_terms] Successfully updated user {self.user_id} accept terms to {datetime.now(timezone.utc)}")
        except Exception as e:
            logger.error(f"[Standalone.update_user_accept_terms] Error updating user {self.user_id} accept terms: {str(e)}", exc_info=True)
            raise UserDatabaseOperationError(str(e))





class InvestigadorService(UserStandaloneService):

    def __init__(self, db: AsyncSession, user_id: str) -> None:
        super().__init__(db, user_id)


    async def update_user(self, user_data: InvestigatorUser,    
                          credits_monthly:int, credits_remaining:int=None,
                          api_key: str = None):
        
        logger.info(f"[Investigator.update_user] Starting update for investigator user {self.user_id}")
        try:
            logger.info(f"[Investigator.update_user] Removing user from all groups")
            self.remove_user_from_all_groups()
            self.remove_user_from_all_roles()

            self.add_keycloak_user_to_group(KeycloakGroup.UserInvestigador)
            self.add_keycloak_user_to_role(Roles.investigador)

            next_reset_credits = None
            if api_key:
                logger.info(f"[Investigator.update_user] API key provided, fetching next reset credits for user {self.user_id}")
                credits_service = CreditsService(db=self.db, user_id=self.user_id)
                credits_service.set_api_key(api_key=api_key)
                _, next_reset_credits = await credits_service.get_api_credits()

            logger.info(f"[Investigator.update_user] Updating user role in database")
            await self.update_user_role_in_db(new_role=RolesDb.investigador, 
                                              report_types=user_data.report_types,
                                              credits_remaining=credits_remaining, 
                                              credits_monthly=credits_monthly,
                                              next_reset_credits=next_reset_credits)

            logger.info(f"[Investigator.update_user] Successfully updated investigator user {self.user_id}")
        except Exception as e:
            logger.error(f"[Investigator.update_user] Error updating investigator user {self.user_id}: {str(e)}", exc_info=True)
            raise UserDatabaseOperationError(str(e))


class AdministradorService(UserStandaloneService):

    def __init__(self, db: AsyncSession, user_id: str) -> None:
        super().__init__(db, user_id)
        self.user_to_modified_id = None


    async def update_user(self, user_data: AdministratorUser = None, credits_monthly: int=None, credits_remaining: int=None, api_key: str = None):
        
        logger.info(f"[Manager.update_user] Starting update for manager user {self.user_id}")
        try:
            logger.info(f"[Manager.update_user] Removing user from all groups")
            self.remove_user_from_all_groups()
            self.remove_user_from_all_roles()

            self.add_keycloak_user_to_group(KeycloakGroup.UserAdministrador)
            self.add_keycloak_user_to_role(Roles.administrador)

            next_reset_credits = None
            if api_key:
                logger.info(f"[Manager.update_user] API key provided, fetching next reset credits for user {self.user_id}")
                credits_service = CreditsService(db=self.db, user_id=self.user_id)
                credits_service.set_api_key(api_key=api_key)
                _, next_reset_credits = await credits_service.get_api_credits()

            if user_data.report_types is None:
                user_data.report_types = [ReportTypes.cpf, ReportTypes.cnpj, ReportTypes.combinado, ReportTypes.email, ReportTypes.telefone, ReportTypes.relacao]

            logger.debug(f"[Manager.update_user] Updating user role in database")
            await self.update_user_role_in_db(new_role=RolesDb.administrador, 
                                              report_types=user_data.report_types,
                                              credits_monthly=credits_monthly, 
                                              credits_remaining=credits_remaining,
                                              next_reset_credits=next_reset_credits)

            logger.info(f"[Manager.update_user] Successfully updated manager user {self.user_id}")
        except Exception as e:
            logger.error(f"[Manager.update_user] Error updating manager user {self.user_id}: {str(e)}", exc_info=True)
            raise UserDatabaseOperationError(str(e))


    async def set_user_to_modified_id(self, user_to_modified):
            self.user_to_modified_id = user_to_modified


    async def set_other_user(self, user_roles, other_user_data_after: OtherUser):
            logger.info("[set_other_user] Called with user_roles: %s, user_to_modified_id: %s, other_user_data_after: %s", user_roles, self.user_to_modified_id, other_user_data_after)
            try:
                if Roles.edit_user in user_roles:    
                    logger.info("[update_other_user_data] User %s has edit user role, proceeding with update", self.user_id)
                    
                    #get the actual data of the user that will be modified 
                    logger.info("[update_other_user_data] Instantiating UserStandaloneService for user_to_modified_id: %s", self.user_to_modified_id)
                    user_being_modified_service = UserStandaloneService(db=self.db, user_id=self.user_to_modified_id)
                    logger.info("[update_other_user_data] Fetching previous user data for user_to_modified_id: %s", self.user_to_modified_id)
                    other_user_data_previous = await user_being_modified_service.get_user_data()
                    logger.info("[update_other_user_data] Previous user data: %s", other_user_data_previous)
                    

                    #compare what changed
                    credits_changed = (other_user_data_previous.credits_monthly != other_user_data_after.credits_monthly)
                    role_changed = other_user_data_previous.role != other_user_data_after.role
                    report_types_changed = (other_user_data_previous.report_types != other_user_data_after.report_types)

                    logger.info("[update_other_user_data] Changes detected - Credits: %s, Role: %s, Report Types: %s", 
                            credits_changed, role_changed, report_types_changed)

                    user_remaining_credits = max(0, (other_user_data_previous.credits +
                                            other_user_data_after.credits_monthly - other_user_data_previous.credits_monthly)
                                            if credits_changed 
                                            else other_user_data_previous.credits)                        
                                        
                    user_monthly_credits = (
                        other_user_data_after.credits_monthly
                        if credits_changed
                        else other_user_data_previous.credits_monthly)


                    logger.info("[update_other_user_data] Calculated credits - Monthly: %s, Remaining: %s", 
                                user_monthly_credits, user_remaining_credits)


                    if role_changed or credits_changed or report_types_changed:
                        logger.info("[update_other_user_data] Only credits changed for user %s with role %s", 
                                self.user_to_modified_id, other_user_data_after.role)
                        service_class = (
                            AdministradorService
                            if other_user_data_after.role == RolesDb.administrador
                            else InvestigadorService
                        )
                        logger.info("[update_other_user_data] Instantiating %s for user %s", service_class.__name__, self.user_to_modified_id)
                        service_instance = service_class(db=self.db, user_id=self.user_to_modified_id)
                        logger.info("[update_other_user_data] Calling update_user for %s with credits_monthly=%s, credits_remaining=%s", service_class.__name__, user_monthly_credits, user_remaining_credits)
                        await service_instance.update_user(
                            user_data = other_user_data_after,
                            credits_monthly=user_monthly_credits,
                            credits_remaining=user_remaining_credits
                        )
                        logger.info("[update_other_user_data] Successfully updated credits for user %s", self.user_to_modified_id)
                    else:
                        logger.info("[update_other_user_data] No changes detected for user %s", self.user_to_modified_id)

                    logger.info("[update_other_user_data] Function completed successfully for user %s", self.user_to_modified_id)

                else:
                    logger.warning("[update_other_user_data] Access denied - User %s does not have edit user role", self.user_id)
                    raise UserEditPermissionDeniedError()
            except Exception as e:
                logger.error("[set_other_user] Exception occurred: %s", str(e), exc_info=True)
                raise UserDatabaseOperationError(str(e))



async def sync_user(access_token: str) -> dict:
    logger.info("[sync_user] Syncing user with Keycloak token claims...")
    user_info = jwt.get_unverified_claims(access_token)
    user_id = user_info.get("sub")
    email = user_info.get("email")
    full_name = user_info.get("name")
    logger.info("[sync_user] Extracted user info: %s", user_info)
    await upsert_user(user_id, email, full_name)


async def upsert_user(user_id: str, email: str, full_name: str):
    logger.info("[upsert_user] Upserting user record...")
    engine = get_database_async_engine()
    salt = create_salt()
    now = datetime.now(pytz.timezone('UTC'))

    query = """
    INSERT INTO public.users ("user_id", "name", "email","salt", "credits", "credits_monthly", "role", "last_login", "report_types")
    VALUES (:user_id, :full_name, :email, :salt, :credits, :credits_monthly, :role, :last_login, :report_types)
    ON CONFLICT ("user_id") 
    DO UPDATE SET "name" = :full_name, "email" = :email, "last_login" = :last_login;
    """

    async with engine.begin() as conn:
        await conn.execute(text(query), {
            "user_id": user_id,
            "full_name": full_name,
            "email": email,
            "last_login": now,
            "salt": salt,
            "credits": None,
            "credits_monthly": None,
            "role": RolesDb.standAlone,
            "report_types": json.dumps([])
        })
    logger.info("[upsert_user] User upsert completed.")
